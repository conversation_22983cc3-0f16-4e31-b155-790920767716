# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 項目概述

Data Sync Hub 是一個基於 GoFrame 框架構建的數據同步中心，集成了：
- MariaDB 關係型數據庫
- Weaviate 向量數據庫
- RabbitMQ 消息隊列
- Nacos 服務發現與配置管理

## 核心構建命令

```bash
# 構建項目
make build

# 生成控制器代碼
make ctrl

# 生成數據訪問層代碼（DAO/DO/Entity）
make dao

# 生成服務接口代碼
make service

# 運行項目
./main

# 或直接運行
go run main.go
```

## 開發工具命令

```bash
# 安裝/更新 GoFrame CLI 工具
make cli

# 更新 GoFrame 到最新版本
make up

# 生成枚舉文件
make enums

# 解析 protobuf 並生成 go 文件
make pb

# 為數據庫表生成 protobuf 文件
make pbentity
```

## Docker 和部署

```bash
# 構建 Docker 鏡像
make image

# 構建並推送 Docker 鏡像
make image.push

# 部署到 kubectl 環境
make deploy
```

## 項目架構

### 服務架構
- **消息隊列模式**: 使用 RabbitMQ 處理異步消息，包含 MariaDB 和 Weaviate 的消息處理器
- **微服務註冊**: 通過 Nacos 進行服務發現和配置管理
- **RESTful API**: 提供查詢、執行和向量操作的 HTTP API 接口

### 核心模塊
1. **API 層** (`api/`): 定義 RESTful API 接口
   - `queryapi`: 數據查詢接口
   - `executeapi`: SQL 執行接口  
   - `vector`: 向量數據庫操作接口

2. **控制器層** (`internal/controller/`): HTTP 請求處理
3. **服務層** (`internal/service/`): 業務邏輯接口定義
4. **邏輯層** (`internal/logic/`): 具體業務邏輯實現
5. **數據層** (`internal/dao/`, `internal/model/`): 數據訪問和模型定義

### 消息隊列架構
- `service.MessageQ().RegisterHandler()`: 註冊消息處理器
- 支持 MariaDB 和 Weaviate 的消息路由
- 在服務啟動時初始化消息接收器

### 配置管理
- 本地配置: `manifest/config/config.yaml`
- Nacos 配置: `manifest/config/configToNacos.yaml`
- 開發配置: `hack/config.yaml`
- 支持 dev/pro 多環境配置

## 代碼生成

項目大量使用 GoFrame CLI 工具進行代碼生成：
- 控制器代碼通過 API 定義自動生成
- 數據庫相關代碼（DAO/DO/Entity）自動生成
- 服務接口代碼自動生成

## 依賴和集成

### 主要依賴
- GoFrame v2.9.0 (Web框架)
- Nacos SDK (服務發現/配置)
- Weaviate Go Client (向量數據庫)
- RabbitMQ AMQP (消息隊列)
- MySQL Driver (數據庫驅動)

### 外部服務
- Nacos: 127.0.0.1:8848 (dev)
- MariaDB: 配置在 hack/config.yaml
- Weaviate: 向量數據庫服務
- RabbitMQ: 消息隊列服務