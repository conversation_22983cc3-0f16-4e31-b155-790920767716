# DataSyncHub SQL 安全防護指南

## 📋 概述

DataSyncHub 已實現了全面的 SQL 安全防護機制，有效防止 SQL 注入攻擊和其他安全威脅。本指南詳細說明了安全功能的使用方法和配置選項。

## 🔒 安全功能特性

### 1. SQL 注入防護
- **語句類型驗證**：只允許預定義的 SQL 操作類型
- **關鍵字過濾**：阻止危險的 SQL 關鍵字和函數
- **注入模式檢測**：識別常見的 SQL 注入攻擊模式
- **語句長度限制**：防止過長的 SQL 語句

### 2. 權限控制
- **操作類型限制**：可配置允許的 SQL 操作類型
- **系統表保護**：阻止對系統表的未授權訪問
- **DDL 操作控制**：可選擇性允許或禁止 DDL 操作

### 3. 審計和監控
- **安全事件記錄**：記錄所有安全相關事件
- **敏感操作追蹤**：特別監控敏感的數據庫操作
- **客戶端 IP 記錄**：追蹤請求來源

## ⚙️ 配置說明

### 默認安全配置

```yaml
security:
  mariadb:
    sql:
      enabled: true
      allowed_operations:
        - "SELECT"
        - "INSERT" 
        - "UPDATE"
        - "DELETE"
        - "CREATE TABLE"
        - "ALTER TABLE"
        - "DROP TABLE"
        - "CREATE INDEX"
        - "DROP INDEX"
      forbidden_keywords:
        - "LOAD_FILE"
        - "INTO OUTFILE"
        - "INTO DUMPFILE"
        - "SYSTEM"
        - "EXEC"
        - "EXECUTE"
        - "CREATE USER"
        - "DROP USER"
        - "GRANT"
        - "REVOKE"
        - "SET PASSWORD"
        - "ALTER USER"
        - "SET GLOBAL"
        - "SET SESSION"
        - "CREATE PROCEDURE"
        - "DROP PROCEDURE"
        - "CREATE FUNCTION"
        - "DROP FUNCTION"
        - "CREATE TRIGGER"
        - "DROP TRIGGER"
        - "CREATE EVENT"
        - "DROP EVENT"
        - "CREATE DATABASE"
        - "DROP DATABASE"
        - "SHUTDOWN"
        - "FLUSH"
        - "RESET"
      max_statement_length: 10000
      allow_ddl: true
      allow_system_tables: false
      max_batch_size: 100
      strict_mode: false
    audit:
      enabled: true
      log_success_operations: false
      log_failed_operations: true
      log_sensitive_operations: true
      retention_days: 30
    rate_limit:
      enabled: true
      max_requests_per_minute: 100
      max_requests_per_hour: 1000
      max_requests_per_day: 10000
```

### 查詢專用配置

對於查詢操作，系統自動使用更嚴格的安全配置：

```yaml
sql:
  allowed_operations: ["SELECT"]
  allow_ddl: false
  strict_mode: true
```

### 管理員配置

管理員級別的配置允許更多操作：

```yaml
sql:
  allow_system_tables: true
  forbidden_keywords:
    - "SHUTDOWN"
    - "LOAD_FILE"
    - "INTO OUTFILE"
    - "INTO DUMPFILE"
  strict_mode: false
```

## 🛡️ 安全檢查項目

### 1. SQL 語句類型檢查
```go
// 允許的操作
✅ SELECT * FROM users WHERE id = 1
✅ INSERT INTO users (name, email) VALUES ('test', '<EMAIL>')
✅ UPDATE users SET name = 'updated' WHERE id = 1
✅ DELETE FROM users WHERE id = 1

// 被阻止的操作
❌ DROP DATABASE production
❌ CREATE USER 'hacker'@'%'
❌ GRANT ALL PRIVILEGES ON *.* TO 'user'@'%'
```

### 2. SQL 注入檢測
```go
// 常見注入模式
❌ SELECT * FROM users WHERE id = 1 UNION SELECT * FROM admin
❌ SELECT * FROM users WHERE name = '' OR 1=1 --
❌ SELECT * FROM users WHERE id = 1; DROP TABLE users;
❌ SELECT LOAD_FILE('/etc/passwd')
❌ SELECT * INTO OUTFILE '/tmp/passwords.txt'
```

### 3. 系統表保護
```go
// 被阻止的系統表訪問
❌ SELECT * FROM information_schema.tables
❌ SELECT * FROM mysql.user
❌ SELECT * FROM performance_schema.events_statements_history
```

## 📊 安全事件監控

### 事件類型

1. **SQL_VALIDATION_FAILED**：SQL 驗證失敗
2. **BATCH_SQL_VALIDATION_FAILED**：批量 SQL 驗證失敗
3. **QUERY_SQL_VALIDATION_FAILED**：查詢 SQL 驗證失敗
4. **SENSITIVE_OPERATION**：敏感操作執行

### 日誌格式

```
[WARN] SQL 安全事件: SQL_VALIDATION_FAILED, SQL: SELECT * FROM users UNION SELECT * FROM admin, 客戶端IP: *************
[WARN] 敏感 SQL 操作: DELETE, 客戶端IP: *************
```

## 🔧 使用示例

### 1. 安全的 SQL 執行

```http
POST /executeapi/v1/executeSQL
Content-Type: application/json

{
  "schema": "dsh",
  "raw_sql": "SELECT id, name, email FROM users WHERE status = 'active' LIMIT 100"
}
```

**回應**：
```json
{
  "code": 0,
  "message": "success",
  "cost": "15ms"
}
```

### 2. 被阻止的危險操作

```http
POST /executeapi/v1/executeSQL
Content-Type: application/json

{
  "schema": "dsh", 
  "raw_sql": "SELECT * FROM users WHERE id = 1 UNION SELECT * FROM admin"
}
```

**回應**：
```json
{
  "code": -1,
  "message": "SQL 語句安全驗證失敗: 檢測到可疑的 SQL 注入模式",
  "cost": "2ms"
}
```

### 3. 批量安全執行

```http
POST /executeapi/v1/batchExecuteSQL
Content-Type: application/json

{
  "schema": "dsh",
  "sql_list": [
    "INSERT INTO users (name, email) VALUES ('張三', '<EMAIL>')",
    "INSERT INTO users (name, email) VALUES ('李四', '<EMAIL>')",
    "UPDATE users SET last_login = NOW() WHERE id IN (1, 2)"
  ]
}
```

## 🚨 安全最佳實踐

### 1. 開發階段
- 使用參數化查詢而非字符串拼接
- 對用戶輸入進行嚴格驗證
- 實施最小權限原則
- 定期進行安全測試

### 2. 部署階段
- 啟用所有安全功能
- 配置適當的審計級別
- 設置合理的限流參數
- 監控安全事件日誌

### 3. 運維階段
- 定期檢查安全日誌
- 更新安全配置
- 監控異常活動
- 建立安全事件響應流程

## 🔍 故障排除

### 常見問題

#### 1. 合法 SQL 被誤判
**問題**：正常的 SQL 語句被安全系統阻止

**解決方案**：
- 檢查 SQL 語句是否包含禁止的關鍵字
- 確認操作類型是否在允許列表中
- 調整安全配置的嚴格程度

#### 2. 性能影響
**問題**：安全檢查影響性能

**解決方案**：
- 優化 SQL 語句長度
- 調整批量執行大小
- 考慮在非生產環境禁用部分檢查

#### 3. 審計日誌過多
**問題**：產生過多的審計日誌

**解決方案**：
- 調整審計配置，只記錄必要事件
- 設置合理的日誌保留期
- 實施日誌輪轉機制

## 📈 性能影響

### 安全檢查開銷
- **SQL 驗證**：約 1-3ms 額外延遲
- **批量驗證**：每條語句約 0.5-1ms
- **審計記錄**：約 0.1-0.5ms

### 優化建議
- 合理設置 `max_statement_length`
- 在開發環境可適當放寬限制
- 使用緩存減少重複驗證

## 🔄 版本更新

### v1.0.0
- 實現基本 SQL 注入防護
- 添加操作類型驗證
- 實現安全事件記錄

### 未來計劃
- 添加機器學習驅動的異常檢測
- 實現更細粒度的權限控制
- 支持自定義安全規則

---

**注意**：安全配置的修改需要重啟服務才能生效。建議在測試環境充分驗證後再應用到生產環境。
