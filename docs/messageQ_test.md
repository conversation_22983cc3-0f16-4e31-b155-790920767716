# MessageQ 測試報告

## 測試環境

### RabbitMQ 環境說明
本地的 RabbitMQ 連線信息是：
```
url: "amqp://admin:admin@127.0.0.1:5672/"
```

## 測試內容

本測試針對 `/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/messageQ/messageQ.go` 文件中的功能進行測試，主要包括以下幾個方面：

1. 創建 MessageQ 實例
2. 註冊消息處理器
3. 分發消息
4. 初始化接收功能（需要實際的 RabbitMQ 服務器）
5. 與 RabbitMQ 的集成（需要實際的 RabbitMQ 服務器）

## 測試方法

測試使用 Go 的標準測試框架和 GoFrame 的測試工具 `gtest` 進行。測試文件位於 `/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/messageQ/messageQ_test.go`。

### 測試用例

1. **Test_New**：測試 `New()` 函數，確保它能夠正確創建 MessageQ 實例。
2. **Test_RegisterHandler**：測試 `RegisterHandler()` 方法，確保它能夠正確註冊消息處理器。
3. **Test_dispatchMessage**：測試 `dispatchMessage()` 方法，確保它能夠根據路由鍵正確分發消息到對應的處理器。
4. **Test_InitReceive**：測試 `InitReceive()` 方法，確保它能夠正確初始化接收功能。（此測試需要實際的 RabbitMQ 服務器，默認跳過）
5. **Test_RabbitMQIntegration**：測試與 RabbitMQ 的集成，確保能夠正確發送和接收消息。（此測試需要實際的 RabbitMQ 服務器，默認跳過）

## 測試結果

```
=== RUN   Test_New
--- PASS: Test_New (0.00s)
=== RUN   Test_RegisterHandler
--- PASS: Test_RegisterHandler (0.00s)
=== RUN   Test_dispatchMessage
2025-06-16T15:06:23.824+08:00 [INFO] {384334ac19744918d036396885ea39b0} receive message route key : test.prefix.something
2025-06-16T15:06:23.824+08:00 [INFO] {384334ac19744918d036396885ea39b0} receive message route key : other.prefix.something
--- PASS: Test_dispatchMessage (0.00s)
=== RUN   Test_InitReceive
    messageQ_test.go:89: 跳過此測試，因為它需要訪問配置
--- SKIP: Test_InitReceive (0.00s)
=== RUN   Test_RabbitMQIntegration
    messageQ_test.go:153: 跳過集成測試，需要實際的 RabbitMQ 服務器
--- SKIP: Test_RabbitMQIntegration (0.00s)
PASS
ok      dataSyncHub/internal/logic/messageQ     0.431s
```

### 結果分析

1. **Test_New**：通過，表明 `New()` 函數能夠正確創建 MessageQ 實例。
2. **Test_RegisterHandler**：通過，表明 `RegisterHandler()` 方法能夠正確註冊消息處理器。
3. **Test_dispatchMessage**：通過，表明 `dispatchMessage()` 方法能夠根據路由鍵正確分發消息到對應的處理器。
4. **Test_InitReceive**：跳過，因為它需要訪問配置。在實際環境中，需要確保配置中包含 `rabbitMQ.url` 項，並設置為正確的 RabbitMQ 連接 URL。
5. **Test_RabbitMQIntegration**：跳過，因為它需要實際的 RabbitMQ 服務器。在有 RabbitMQ 服務器的環境中，可以取消跳過，進行完整的集成測試。

## 如何運行完整測試

要運行完整的測試，包括與 RabbitMQ 的集成測試，需要以下步驟：

1. 確保本地有運行中的 RabbitMQ 服務器，連接 URL 為 `amqp://admin:admin@127.0.0.1:5672/`。
2. 修改測試文件 `messageQ_test.go`，取消 `Test_InitReceive` 和 `Test_RabbitMQIntegration` 測試函數中的 `t.Skip()` 調用。
3. 運行測試：
   ```
   cd /Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/messageQ
   go test -v
   ```

## 注意事項

1. 測試 `InitReceive()` 方法和與 RabbitMQ 的集成需要實際的 RabbitMQ 服務器。
2. 在沒有 RabbitMQ 服務器的環境中，相關測試會被跳過，不會導致測試失敗。
3. 在實際環境中，需要確保配置中包含 `rabbitMQ.url` 項，並設置為正確的 RabbitMQ 連接 URL。