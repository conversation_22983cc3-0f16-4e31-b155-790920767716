package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type ExecuteSQLReq struct {
	g.Meta `path:"/v1/executeSQL" tags:"Execute SQL" method:"post" summary:"execute sql"`
	Schema string `json:"schema" v:"required"`
	RawSQL string `json:"raw_sql" v:"required"`
}
type ExecuteSQLRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Cost    string `json:"cost"`
}
type BatchExecuteSQLReq struct {
	g.Meta  `path:"/v1/batchExecuteSQL" tags:"Execute SQL" method:"post" summary:"batch execute sql"`
	Schema  string   `json:"schema" v:"required"`
	SQLList []string `json:"sql_list" v:"required"`
}

type BatchExecuteSQLRes struct {
	Code         int      `json:"code"`
	Message      string   `json:"message"`
	Cost         string   `json:"cost"`
	TotalCount   int      `json:"total_count"`
	SuccessCount int      `json:"success_count"`
	FailCount    int      `json:"fail_count"`
	Errors       []string `json:"errors"`
}
