// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package executeapi

import (
	"context"

	"dataSyncHub/api/executeapi/v1"
)

type IExecuteapiV1 interface {
	ExecuteSQL(ctx context.Context, req *v1.ExecuteSQLReq) (res *v1.ExecuteSQLRes, err error)
	BatchExecuteSQL(ctx context.Context, req *v1.BatchExecuteSQLReq) (res *v1.BatchExecuteSQLRes, err error)
}
