// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package queryapi

import (
	"context"

	"dataSyncHub/api/queryapi/v1"
)

type IQueryapiV1 interface {
	DataQuery(ctx context.Context, req *v1.DataQueryReq) (res *v1.DataQueryRes, err error)
}
