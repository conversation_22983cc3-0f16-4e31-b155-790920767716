package v1

import (
	"dataSyncHub/internal/model"

	"github.com/gogf/gf/v2/frame/g"
)

type FetchRecordsReq struct {
	g.Meta `path:"/v1/fetchRecords" tags:"vector" method:"post" summary:"fetch records"`
	model.FetchRecordsInput
}
type FetchRecordsRes struct {
	model.FetchRecordsOutput
	BaseRes
}

type GetAllRecordsReq struct {
	g.Meta `path:"/v1/getAllRecords" tags:"vector" method:"post" summary:"get all records"`
	model.GetAllRecordsInput
}
type GetAllRecordsRes struct {
	model.GetAllRecordsOutput
	BaseRes
}
type GetPropertyNamesReq struct {
	g.Meta     `path:"/v1/getPropertyNames" tags:"vector" method:"post" summary:"get property names"`
	Collection string `json:"collection" v:""`
}
type GetPropertyNamesRes struct {
	Names []string `json:"names"`
	BaseRes
}
type GetPropertiesByGroupReq struct {
	g.Meta `path:"/v1/getPropertiesByGroup" tags:"vector" method:"post" summary:"get properties by group"`
	model.GetPropertiesByGroupInput
}

type GetPropertiesByGroupRes struct {
	model.GetPropertiesByGroupOutput
	BaseRes
}
type SimilaritySearchReq struct {
	g.Meta `path:"/v1/similaritySearch" tags:"vector" method:"post" summary:"similarity search"`
	model.SimilaritySearchInput
}
type SimilaritySearchRes struct {
	model.SimilaritySearchOutput
	BaseRes
}
type HybridSearchReq struct {
	g.Meta `path:"/v1/hybridSearch" tags:"vector" method:"post" summary:"hybrid search"`
	model.HybridSearchInput
}
type HybridSearchRes struct {
	model.HybridSearchOutput
	BaseRes
}
type GetTenantAndCollectionsReq struct {
	g.Meta      `path:"/v1/getTenantAndCollections" tags:"vector" method:"post" summary:"get tenant and collections"`
	Collections []string `json:"collections" v:"required"`
}
type GetTenantAndCollectionsRes struct {
	TenantCollections g.Map `json:"tenant_collections"`
	BaseRes
}
type CreateDataReq struct {
	g.Meta `path:"/v1/createData" tags:"vector" method:"post" summary:"create single data"`
	model.CollectionData
}
type CreateDataRes struct {
	ID string `json:"id"`
	BaseRes
}
type BatchCreateDataReq struct {
	g.Meta `path:"/v1/batchCreateData" tags:"vector" method:"post" summary:"batch create data"`
	model.CreateDataInput
}
type BatchCreateDataRes struct {
	model.CreateDataOutput
	BaseRes
}

type CreateCollectionReq struct {
	g.Meta `path:"/v1/createCollection" tags:"vector" method:"post" summary:"create collection"`
	model.CreateCollectionInput
}
type CreateCollectionRes struct {
	BaseRes
}
type GetPropertiesReq struct {
	g.Meta `path:"/v1/getProperties" tags:"vector" method:"post" summary:"get properties"`
	model.GetPropertiesInput
}
type GetPropertiesRes struct {
	model.GetPropertiesOutput
	BaseRes
}
type BaseRes struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Cost    string `json:"cost"`
}
