// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package vector

import (
	"context"

	"dataSyncHub/api/vector/v1"
)

type IVectorV1 interface {
	FetchRecords(ctx context.Context, req *v1.FetchRecordsReq) (res *v1.FetchRecordsRes, err error)
	GetAllRecords(ctx context.Context, req *v1.GetAllRecordsReq) (res *v1.GetAllRecordsRes, err error)
	GetPropertyNames(ctx context.Context, req *v1.GetPropertyNamesReq) (res *v1.GetPropertyNamesRes, err error)
	GetPropertiesByGroup(ctx context.Context, req *v1.GetPropertiesByGroupReq) (res *v1.GetPropertiesByGroupRes, err error)
	SimilaritySearch(ctx context.Context, req *v1.SimilaritySearchReq) (res *v1.SimilaritySearchRes, err error)
	HybridSearch(ctx context.Context, req *v1.HybridSearchReq) (res *v1.HybridSearchRes, err error)
	GetTenantAndCollections(ctx context.Context, req *v1.GetTenantAndCollectionsReq) (res *v1.GetTenantAndCollectionsRes, err error)
	CreateData(ctx context.Context, req *v1.CreateDataReq) (res *v1.CreateDataRes, err error)
	BatchCreateData(ctx context.Context, req *v1.BatchCreateDataReq) (res *v1.BatchCreateDataRes, err error)
	CreateCollection(ctx context.Context, req *v1.CreateCollectionReq) (res *v1.CreateCollectionRes, err error)
	GetProperties(ctx context.Context, req *v1.GetPropertiesReq) (res *v1.GetPropertiesRes, err error)
}
