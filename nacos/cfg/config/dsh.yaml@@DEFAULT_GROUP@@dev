server:
  address:     ":8087"
  openapiPath: "/api.json"
  swaggerPath: "/swagger" 
logger:
  level: "all"
  stdout: true
  path: ./logs
  file: dsh_{Y-m-d}.log
  RotateExpire: "1d"
  RotateBackupLimit: 1
  RotateBackupExpire: "7d"
  RotateBackupCompress: 9
database:
  default:
    link: "mysql:root:wilsonliu123@tcp(127.0.0.1:3306)?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  dsh:
    link: "mysql:root:wilsonliu123@tcp(127.0.0.1:3306)/dsh?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
  logger:
    path: "logs/db"
    level: "all"
    stdout: false
    rotateExpire: "1d"
    rotateBackupLimit: 1
    rotateBackupExpire: "7d"
    rotateBackupCompress: 9
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"
vectorEmbeddings:
  # 所採用的embedding 參數設定
  embedding:
    # 提供者： azure,google. google又分爲 studio,vertex
    provider: azure
    azure:
      resourceName: aile-chatbot
      deploymentId: text-embedding-ada-002
      # 設定環境變量或者是key 推薦是環境變量名稱
      api_key: 368328c0b03b4ff09ae91c6d0df5cfae
    google:
      # provider 為google.studio 的時 projectId 不需要設置
      projectId:
      modelId:
      # provider 如果設定 google.studio 時候要設定studio api key
      # provider 如果設定 google.vertex 則採用設置環境變數到docker compose 的方式
      studioAPIKey:




weaviate:
  host: localhost:8080
  scheme: http
