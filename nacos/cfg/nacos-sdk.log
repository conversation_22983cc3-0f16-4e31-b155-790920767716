2025-06-12T12:30:35.518+0800	WARN	config_client/config_client.go:328	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T12:30:35.518+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:09:57.805+0800	WARN	config_client/config_client.go:328	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:09:57.805+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:09:57.805+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:09:57.805+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-5aa6da29-4f8c-4d67-b25c-dd8d0863f808)
2025-06-12T14:09:57.805+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:09:57.805+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:09:57.805+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:09:57.805+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-5aa6da29-4f8c-4d67-b25c-dd8d0863f808 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:09:57.805+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:09:57.805+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:09:57.805+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:09:57.908+0800	INFO	rpc/rpc_client.go:337	config-0-5aa6da29-4f8c-4d67-b25c-dd8d0863f808 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708597806_192.168.215.1_43430
2025-06-12T14:09:57.917+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:11:58.204+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:11:58.205+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:11:58.205+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4c53dc85-8eda-44d6-b70a-aa635205e178)
2025-06-12T14:11:58.205+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:11:58.205+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:11:58.205+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:11:58.205+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4c53dc85-8eda-44d6-b70a-aa635205e178 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:11:58.205+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:11:58.205+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:11:58.205+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:11:58.307+0800	INFO	rpc/rpc_client.go:337	config-0-4c53dc85-8eda-44d6-b70a-aa635205e178 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708718206_192.168.215.1_45933
2025-06-12T14:11:58.318+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:13:18.708+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:13:18.709+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:13:18.709+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-f11c1e3a-1c84-4382-ae31-22554b291e5c)
2025-06-12T14:13:18.709+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:13:18.709+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:13:18.709+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:13:18.709+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-f11c1e3a-1c84-4382-ae31-22554b291e5c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:13:18.709+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:13:18.709+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:13:18.709+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:13:18.811+0800	INFO	rpc/rpc_client.go:337	config-0-f11c1e3a-1c84-4382-ae31-22554b291e5c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708798710_192.168.215.1_45224
2025-06-12T14:13:18.821+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:15:21.319+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:15:21.320+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:15:21.320+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9bb01b68-89a0-4ff5-915c-b3a6f7c33c8e)
2025-06-12T14:15:21.320+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:15:21.320+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:15:21.320+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:15:21.320+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9bb01b68-89a0-4ff5-915c-b3a6f7c33c8e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:15:21.320+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:15:21.320+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:15:21.320+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:15:21.424+0800	INFO	rpc/rpc_client.go:337	config-0-9bb01b68-89a0-4ff5-915c-b3a6f7c33c8e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708921322_192.168.215.1_24755
2025-06-12T14:15:21.444+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:16:42.079+0800	ERROR	rpc/rpc_client.go:525	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded while waiting for connections to become ready
2025-06-12T14:18:00.498+0800	INFO	rpc/rpc_client.go:486	813ebe34-96b1-4135-9a00-0bafa941c041 notify connected event to listeners , connectionId=1749709080394_192.168.215.1_19423
2025-06-12T14:18:00.580+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:18:00.581+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:18:00.581+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b7b6800e-f176-4e63-b950-3544f3053bf6)
2025-06-12T14:18:00.581+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:18:00.581+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:18:00.581+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:18:00.581+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b7b6800e-f176-4e63-b950-3544f3053bf6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:18:00.581+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:18:00.581+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:18:00.581+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:18:01.129+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:18:01.130+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:18:01.130+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e127cc2a-d033-427e-9412-43a21450b42f)
2025-06-12T14:18:01.130+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:18:01.130+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:18:01.130+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:18:01.130+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e127cc2a-d033-427e-9412-43a21450b42f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:18:01.130+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:18:01.130+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:18:01.130+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:18:01.232+0800	INFO	rpc/rpc_client.go:337	config-0-e127cc2a-d033-427e-9412-43a21450b42f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709081131_192.168.215.1_48260
2025-06-12T14:18:01.242+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:21:21.944+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:21:21.944+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:21:21.944+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0159827b-3746-4c1c-b34a-03fd4c4dadf4)
2025-06-12T14:21:21.944+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:21:21.944+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:21:21.944+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:21:21.944+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0159827b-3746-4c1c-b34a-03fd4c4dadf4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:21:21.944+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:21:21.944+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:21:21.944+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:21:22.047+0800	INFO	rpc/rpc_client.go:337	config-0-0159827b-3746-4c1c-b34a-03fd4c4dadf4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709281945_192.168.215.1_18835
2025-06-12T14:21:22.059+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:22:57.127+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:22:57.127+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:22:57.127+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-bd976f31-2c06-4aa1-879b-f138be2262d1)
2025-06-12T14:22:57.128+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:22:57.128+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:22:57.128+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:22:57.128+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-bd976f31-2c06-4aa1-879b-f138be2262d1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:22:57.128+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:22:57.128+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:22:57.128+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:22:57.231+0800	INFO	rpc/rpc_client.go:337	config-0-bd976f31-2c06-4aa1-879b-f138be2262d1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709377130_192.168.215.1_30060
2025-06-12T14:22:57.251+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:24:55.381+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:24:55.381+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:24:55.381+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6d9e2f2b-a6b8-4542-bcfc-d5c6114e00fc)
2025-06-12T14:24:55.381+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:24:55.381+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:24:55.381+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:24:55.381+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6d9e2f2b-a6b8-4542-bcfc-d5c6114e00fc try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:24:55.381+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:24:55.381+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:24:55.381+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:24:55.484+0800	INFO	rpc/rpc_client.go:337	config-0-6d9e2f2b-a6b8-4542-bcfc-d5c6114e00fc success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709495382_192.168.215.1_47224
2025-06-12T14:24:55.496+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:24:59.644+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:24:59.644+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:24:59.645+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-11de90c4-1f38-4c86-b4c9-e2782011d6f9)
2025-06-12T14:24:59.645+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:24:59.645+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:24:59.645+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:24:59.645+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-11de90c4-1f38-4c86-b4c9-e2782011d6f9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:24:59.645+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:24:59.645+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:24:59.645+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:24:59.747+0800	INFO	rpc/rpc_client.go:337	config-0-11de90c4-1f38-4c86-b4c9-e2782011d6f9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709499646_192.168.215.1_45166
2025-06-12T14:24:59.761+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:32:59.427+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<default> with instance:<*************:8087@DEFAULT>
2025-06-12T14:53:19.434+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:53:19.434+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:53:19.434+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-60814a4e-0054-4291-87e7-01042d057fa1)
2025-06-12T14:53:19.434+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:53:19.434+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:53:19.434+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:53:19.434+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-60814a4e-0054-4291-87e7-01042d057fa1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:53:19.434+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:53:19.434+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:53:19.434+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:53:19.537+0800	INFO	rpc/rpc_client.go:337	config-0-60814a4e-0054-4291-87e7-01042d057fa1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711199435_192.168.215.1_22138
2025-06-12T14:53:19.551+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:53:55.794+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:53:55.794+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:53:55.794+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9fecb98a-ddc8-45a2-9221-4fcf0f2118d9)
2025-06-12T14:53:55.794+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:53:55.794+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:53:55.794+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:53:55.794+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9fecb98a-ddc8-45a2-9221-4fcf0f2118d9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:53:55.794+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:53:55.794+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:53:55.794+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:53:55.898+0800	INFO	rpc/rpc_client.go:337	config-0-9fecb98a-ddc8-45a2-9221-4fcf0f2118d9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711235796_192.168.215.1_62807
2025-06-12T14:53:55.907+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:55:33.575+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<default> with instance:<*************:8087@DEFAULT>
2025-06-12T14:58:34.754+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:58:34.754+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:58:34.755+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-27f2653e-588e-4ad6-bf1e-042e30b045ce)
2025-06-12T14:58:34.755+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:58:34.755+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:58:34.755+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:58:34.755+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-27f2653e-588e-4ad6-bf1e-042e30b045ce try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:58:34.755+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:58:34.755+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:58:34.755+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:58:34.858+0800	INFO	rpc/rpc_client.go:337	config-0-27f2653e-588e-4ad6-bf1e-042e30b045ce success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711514756_192.168.215.1_63457
2025-06-12T14:58:34.874+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T14:59:46.223+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T14:59:46.223+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T14:59:46.223+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9ddfddf2-b86a-42af-ae83-ee707da06546)
2025-06-12T14:59:46.224+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:59:46.224+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:59:46.224+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:59:46.224+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9ddfddf2-b86a-42af-ae83-ee707da06546 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:59:46.224+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:59:46.224+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:59:46.224+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:59:46.326+0800	INFO	rpc/rpc_client.go:337	config-0-9ddfddf2-b86a-42af-ae83-ee707da06546 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711586225_192.168.215.1_45768
2025-06-12T14:59:46.338+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T15:00:33.775+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T15:00:33.775+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T15:00:33.775+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4ee3ee4c-08db-4282-b18b-6553088d9f6a)
2025-06-12T15:00:33.775+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:00:33.775+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:00:33.775+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:00:33.775+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4ee3ee4c-08db-4282-b18b-6553088d9f6a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:00:33.775+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:00:33.775+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:00:33.775+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:00:33.878+0800	INFO	rpc/rpc_client.go:337	config-0-4ee3ee4c-08db-4282-b18b-6553088d9f6a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711633776_192.168.215.1_55089
2025-06-12T15:00:33.884+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T17:44:09.967+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<default> with instance:<*************:8087@DEFAULT>
2025-06-12T17:44:14.831+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:44:14.832+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:44:14.832+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-5d8b9002-eba2-47fe-8ed2-8f71128c0264)
2025-06-12T17:44:14.832+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:44:14.832+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:44:14.832+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:44:14.832+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-5d8b9002-eba2-47fe-8ed2-8f71128c0264 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:44:14.832+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:44:14.832+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:44:14.832+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:44:14.935+0800	INFO	rpc/rpc_client.go:337	config-0-5d8b9002-eba2-47fe-8ed2-8f71128c0264 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721454833_192.168.215.1_55331
2025-06-12T17:44:14.945+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<default> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T17:44:51.766+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<default> with instance:<*************:8087@DEFAULT>
2025-06-12T17:45:47.356+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:45:47.356+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:45:47.356+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9e146668-4f91-485f-bc6f-d54022e412aa)
2025-06-12T17:45:47.357+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:45:47.357+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:45:47.357+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:45:47.357+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9e146668-4f91-485f-bc6f-d54022e412aa try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:45:47.357+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:45:47.357+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:45:47.357+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:45:47.460+0800	INFO	rpc/rpc_client.go:337	config-0-9e146668-4f91-485f-bc6f-d54022e412aa success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721547358_192.168.215.1_50966
2025-06-12T17:45:47.470+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T17:54:34.799+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-12T17:58:09.805+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:58:09.805+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:58:09.805+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4ee9a00d-c66d-40ae-b046-de32c3aa4d66)
2025-06-12T17:58:09.805+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:58:09.805+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:58:09.805+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:58:09.805+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4ee9a00d-c66d-40ae-b046-de32c3aa4d66 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:58:09.805+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:58:09.805+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:58:09.805+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:58:09.907+0800	INFO	rpc/rpc_client.go:337	config-0-4ee9a00d-c66d-40ae-b046-de32c3aa4d66 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722289814_192.168.215.1_53686
2025-06-12T17:58:09.920+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:06:37.096+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-12T18:06:43.167+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:06:43.167+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:06:43.167+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-61a3f559-f896-40d9-821f-5a23bf880ce6)
2025-06-12T18:06:43.167+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:43.167+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:43.167+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:43.167+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-61a3f559-f896-40d9-821f-5a23bf880ce6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:43.167+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:43.167+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:43.167+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:43.270+0800	INFO	rpc/rpc_client.go:337	config-0-61a3f559-f896-40d9-821f-5a23bf880ce6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722803168_192.168.215.1_57051
2025-06-12T18:06:43.276+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:14:29.155+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-12T18:14:32.841+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:14:32.841+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:14:32.841+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-63b2ae04-9ebd-42ae-9ac4-31cc91552be0)
2025-06-12T18:14:32.841+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:14:32.841+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:14:32.842+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:14:32.842+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-63b2ae04-9ebd-42ae-9ac4-31cc91552be0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:14:32.842+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:14:32.842+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:14:32.842+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:14:32.945+0800	INFO	rpc/rpc_client.go:337	config-0-63b2ae04-9ebd-42ae-9ac4-31cc91552be0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723272843_192.168.215.1_36518
2025-06-12T18:14:32.956+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:14:35.138+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-12T18:22:33.597+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:22:33.598+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:22:33.598+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-1f267948-ce77-4bfe-a962-2034b1445b93)
2025-06-12T18:22:33.598+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:22:33.598+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:22:33.598+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:22:33.598+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-1f267948-ce77-4bfe-a962-2034b1445b93 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:22:33.598+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:22:33.598+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:22:33.598+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:22:33.702+0800	INFO	rpc/rpc_client.go:337	config-0-1f267948-ce77-4bfe-a962-2034b1445b93 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723753576_192.168.215.1_27283
2025-06-12T18:22:33.714+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-13T10:33:57.411+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-13T10:33:57.411+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-13T10:33:57.411+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-37a563dd-2d2e-486d-bdf5-5eab15497736)
2025-06-13T10:33:57.411+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:33:57.411+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:33:57.411+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:33:57.411+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-37a563dd-2d2e-486d-bdf5-5eab15497736 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:33:57.411+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:33:57.411+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:33:57.411+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:33:57.514+0800	INFO	rpc/rpc_client.go:337	config-0-37a563dd-2d2e-486d-bdf5-5eab15497736 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749782037412_192.168.215.1_40599
2025-06-13T10:33:57.525+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-13T10:50:46.307+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-13T10:50:46.308+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-13T10:50:46.308+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9a912931-f108-4d04-a39a-20b4427be844)
2025-06-13T10:50:46.308+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:50:46.308+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:50:46.308+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:50:46.308+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9a912931-f108-4d04-a39a-20b4427be844 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:50:46.308+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:50:46.308+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:50:46.308+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:50:46.411+0800	INFO	rpc/rpc_client.go:337	config-0-9a912931-f108-4d04-a39a-20b4427be844 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783046310_192.168.215.1_42170
2025-06-13T10:50:46.424+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-13T10:50:58.328+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-13T10:51:00.748+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-13T10:51:00.748+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-13T10:51:00.748+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-600e0c1d-b3d2-4ad8-ba77-886d6ec3a4dc)
2025-06-13T10:51:00.748+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:51:00.748+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:51:00.748+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:51:00.748+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-600e0c1d-b3d2-4ad8-ba77-886d6ec3a4dc try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:51:00.749+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:51:00.749+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:51:00.749+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:51:00.854+0800	INFO	rpc/rpc_client.go:337	config-0-600e0c1d-b3d2-4ad8-ba77-886d6ec3a4dc success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783060752_192.168.215.1_31710
2025-06-13T10:51:00.861+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-13T10:51:47.177+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-13T10:51:47.177+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-13T10:51:47.177+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2301ef3a-d140-462f-bedf-42563fa85d86)
2025-06-13T10:51:47.177+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:51:47.177+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:51:47.177+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:51:47.177+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2301ef3a-d140-462f-bedf-42563fa85d86 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:51:47.178+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:51:47.178+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:51:47.178+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:51:47.281+0800	INFO	rpc/rpc_client.go:337	config-0-2301ef3a-d140-462f-bedf-42563fa85d86 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783107178_192.168.215.1_24724
2025-06-13T10:51:47.289+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-13T10:54:11.370+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-13T10:54:11.371+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-13T10:54:11.371+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2fb652ea-2bc2-4fcc-8d00-2a3ef7cff144)
2025-06-13T10:54:11.371+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:54:11.371+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:54:11.371+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:54:11.371+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2fb652ea-2bc2-4fcc-8d00-2a3ef7cff144 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:54:11.371+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:54:11.371+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:54:11.371+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:54:11.474+0800	INFO	rpc/rpc_client.go:337	config-0-2fb652ea-2bc2-4fcc-8d00-2a3ef7cff144 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783251372_192.168.215.1_63133
2025-06-13T10:54:11.485+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T16:10:59.473+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T16:10:59.474+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T16:10:59.474+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9236b6f6-e183-4fa7-add9-58dee007ff77)
2025-06-14T16:10:59.474+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T16:10:59.474+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T16:10:59.474+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T16:10:59.474+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9236b6f6-e183-4fa7-add9-58dee007ff77 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T16:10:59.474+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T16:10:59.474+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T16:10:59.474+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T16:10:59.576+0800	INFO	rpc/rpc_client.go:337	config-0-9236b6f6-e183-4fa7-add9-58dee007ff77 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749888659498_192.168.215.1_38715
2025-06-14T16:10:59.586+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T20:44:01.129+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T20:44:01.129+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T20:44:01.129+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2f80942f-7d52-466e-a6a8-2e74137cf857)
2025-06-14T20:44:01.129+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T20:44:01.129+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T20:44:01.129+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T20:44:01.129+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2f80942f-7d52-466e-a6a8-2e74137cf857 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T20:44:01.129+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T20:44:01.129+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T20:44:01.129+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T20:44:01.231+0800	INFO	rpc/rpc_client.go:337	config-0-2f80942f-7d52-466e-a6a8-2e74137cf857 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749905041130_192.168.215.1_39499
2025-06-14T20:44:01.240+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T20:58:28.465+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-14T20:58:37.982+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T20:58:37.982+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T20:58:37.983+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0eb3d4be-8351-41e2-aca7-b9964dc9e94a)
2025-06-14T20:58:37.983+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T20:58:37.983+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T20:58:37.983+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T20:58:37.983+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0eb3d4be-8351-41e2-aca7-b9964dc9e94a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T20:58:37.983+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T20:58:37.983+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T20:58:37.983+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T20:58:38.085+0800	INFO	rpc/rpc_client.go:337	config-0-0eb3d4be-8351-41e2-aca7-b9964dc9e94a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749905918048_192.168.215.1_58090
2025-06-14T20:58:38.095+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T21:16:51.114+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-14T21:17:26.541+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T21:17:26.541+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T21:17:26.541+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2f00288e-96c0-433b-a505-73bb677db69e)
2025-06-14T21:17:26.541+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:17:26.541+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:17:26.541+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:17:26.541+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2f00288e-96c0-433b-a505-73bb677db69e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:17:26.541+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:17:26.541+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:17:26.541+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:17:26.643+0800	INFO	rpc/rpc_client.go:337	config-0-2f00288e-96c0-433b-a505-73bb677db69e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749907046542_192.168.215.1_59697
2025-06-14T21:17:26.651+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T21:19:05.049+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-14T21:19:08.703+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T21:19:08.703+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T21:19:08.703+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d7442e34-9761-4085-be95-c7d6b5e9354b)
2025-06-14T21:19:08.703+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:19:08.703+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:19:08.703+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:19:08.703+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d7442e34-9761-4085-be95-c7d6b5e9354b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:19:08.703+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:19:08.703+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:19:08.703+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:19:08.806+0800	INFO	rpc/rpc_client.go:337	config-0-d7442e34-9761-4085-be95-c7d6b5e9354b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749907148704_192.168.215.1_63661
2025-06-14T21:19:08.815+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T21:56:33.009+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T21:56:33.010+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T21:56:33.010+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-55e2c40b-0719-4781-a808-79a82b8e8fd8)
2025-06-14T21:56:33.010+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:56:33.010+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:56:33.010+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:56:33.010+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-55e2c40b-0719-4781-a808-79a82b8e8fd8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:56:33.010+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:56:33.010+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:56:33.010+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:56:33.112+0800	INFO	rpc/rpc_client.go:337	config-0-55e2c40b-0719-4781-a808-79a82b8e8fd8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749909393012_192.168.215.1_24638
2025-06-14T21:56:33.120+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-14T22:19:19.395+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-14T22:19:23.079+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-14T22:19:23.079+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-14T22:19:23.079+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a983ed02-2274-4fe9-b0e3-568597bf95ce)
2025-06-14T22:19:23.079+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T22:19:23.079+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T22:19:23.079+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T22:19:23.080+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a983ed02-2274-4fe9-b0e3-568597bf95ce try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T22:19:23.080+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T22:19:23.080+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T22:19:23.080+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T22:19:23.182+0800	INFO	rpc/rpc_client.go:337	config-0-a983ed02-2274-4fe9-b0e3-568597bf95ce success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749910763036_192.168.215.1_51175
2025-06-14T22:19:23.194+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T06:56:59.961+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T06:56:59.961+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T06:56:59.961+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-eab8470a-762f-4a28-b18d-2911018524e6)
2025-06-15T06:56:59.962+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T06:56:59.962+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T06:56:59.962+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T06:56:59.962+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-eab8470a-762f-4a28-b18d-2911018524e6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T06:56:59.962+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T06:56:59.962+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T06:56:59.962+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T06:57:00.065+0800	INFO	rpc/rpc_client.go:337	config-0-eab8470a-762f-4a28-b18d-2911018524e6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749941819963_192.168.215.1_30696
2025-06-15T06:57:00.081+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T07:55:50.328+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T07:55:50.329+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T07:55:50.329+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b7a4db65-742d-414a-854e-55cdb0d5794e)
2025-06-15T07:55:50.329+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T07:55:50.330+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T07:55:50.330+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T07:55:50.330+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b7a4db65-742d-414a-854e-55cdb0d5794e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T07:55:50.330+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T07:55:50.330+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T07:55:50.330+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T07:55:50.433+0800	INFO	rpc/rpc_client.go:337	config-0-b7a4db65-742d-414a-854e-55cdb0d5794e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749945350327_192.168.215.1_26709
2025-06-15T07:55:50.445+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T07:55:59.936+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-15T15:10:40.642+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:10:40.642+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:10:40.642+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3d22608b-6647-471a-9240-409d4bbac458)
2025-06-15T15:10:40.642+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:10:40.643+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:10:40.643+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:10:40.643+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3d22608b-6647-471a-9240-409d4bbac458 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:10:40.643+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:10:40.643+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:10:40.643+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:10:40.746+0800	INFO	rpc/rpc_client.go:337	config-0-3d22608b-6647-471a-9240-409d4bbac458 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971440646_192.168.215.1_57281
2025-06-15T15:10:40.757+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:15:31.962+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:15:31.963+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:15:31.963+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-af989126-0ba9-4cfa-8049-209d3358965a)
2025-06-15T15:15:31.963+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:15:31.963+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:15:31.963+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:15:31.964+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-af989126-0ba9-4cfa-8049-209d3358965a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:15:31.964+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:15:31.964+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:15:31.964+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:15:32.066+0800	INFO	rpc/rpc_client.go:337	config-0-af989126-0ba9-4cfa-8049-209d3358965a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971731965_192.168.215.1_55834
2025-06-15T15:15:32.081+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:17:57.981+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:17:57.981+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:17:57.981+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-68d23987-6571-4aaa-a303-c88ec14f911b)
2025-06-15T15:17:57.981+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:17:57.981+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:17:57.981+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:17:57.981+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-68d23987-6571-4aaa-a303-c88ec14f911b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:17:57.981+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:17:57.981+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:17:57.981+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:17:58.083+0800	INFO	rpc/rpc_client.go:337	config-0-68d23987-6571-4aaa-a303-c88ec14f911b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971877982_192.168.215.1_25818
2025-06-15T15:17:58.093+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:23:12.376+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:23:12.377+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:23:12.377+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7a61dd12-691d-4594-bfbb-685f75abe00f)
2025-06-15T15:23:12.377+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:23:12.377+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:23:12.377+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:23:12.377+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7a61dd12-691d-4594-bfbb-685f75abe00f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:23:12.377+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:23:12.377+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:23:12.377+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:23:12.478+0800	INFO	rpc/rpc_client.go:337	config-0-7a61dd12-691d-4594-bfbb-685f75abe00f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972192378_192.168.215.1_50072
2025-06-15T15:23:12.486+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:23:58.470+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:23:58.470+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:23:58.470+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c9f66394-6d1f-44ed-a2c4-fbe586deb503)
2025-06-15T15:23:58.470+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:23:58.470+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:23:58.470+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:23:58.470+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c9f66394-6d1f-44ed-a2c4-fbe586deb503 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:23:58.470+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:23:58.470+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:23:58.470+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:23:58.572+0800	INFO	rpc/rpc_client.go:337	config-0-c9f66394-6d1f-44ed-a2c4-fbe586deb503 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972238471_192.168.215.1_57562
2025-06-15T15:23:58.585+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:25:14.981+0800	INFO	rpc/rpc_client.go:486	fdc3bbb7-238d-44e1-8c2e-39049f6aceeb notify connected event to listeners , connectionId=1749972314863_192.168.215.1_36240
2025-06-15T15:25:15.050+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:25:15.050+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:25:15.050+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-60b0669b-a6c0-45f5-8107-8594ac5545f5)
2025-06-15T15:25:15.050+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:25:15.050+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:25:15.050+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:25:15.050+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-60b0669b-a6c0-45f5-8107-8594ac5545f5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:25:15.051+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:25:15.051+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:25:15.051+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:25:15.153+0800	INFO	rpc/rpc_client.go:337	config-0-60b0669b-a6c0-45f5-8107-8594ac5545f5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972315052_192.168.215.1_59795
2025-06-15T15:25:15.161+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:39:56.085+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:39:56.085+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:39:56.085+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b7ecc173-60b9-4bb9-ad0c-de9f19c52496)
2025-06-15T15:39:56.085+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:39:56.085+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:39:56.085+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:39:56.085+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b7ecc173-60b9-4bb9-ad0c-de9f19c52496 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:39:56.085+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:39:56.085+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:39:56.085+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:39:56.188+0800	INFO	rpc/rpc_client.go:337	config-0-b7ecc173-60b9-4bb9-ad0c-de9f19c52496 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973196086_192.168.215.1_24717
2025-06-15T15:39:56.200+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:42:00.916+0800	INFO	rpc/rpc_client.go:486	7fdcfa6d-731c-48cf-a12f-105032df28d9 notify connected event to listeners , connectionId=1749973320813_192.168.215.1_61725
2025-06-15T15:42:00.982+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:42:00.982+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:42:00.982+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b0dd1001-23ef-483e-b40a-7abfe2c0a277)
2025-06-15T15:42:00.982+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:00.982+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:00.982+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:00.982+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b0dd1001-23ef-483e-b40a-7abfe2c0a277 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:00.982+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:00.982+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:00.982+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:01.085+0800	INFO	rpc/rpc_client.go:337	config-0-b0dd1001-23ef-483e-b40a-7abfe2c0a277 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973320983_192.168.215.1_48427
2025-06-15T15:42:01.092+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:42:03.407+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:42:03.407+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:42:03.407+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-69e37497-071a-4f94-9564-0be4777c37e0)
2025-06-15T15:42:03.407+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:03.407+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:03.407+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:03.407+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-69e37497-071a-4f94-9564-0be4777c37e0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:03.407+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:03.407+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:03.407+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:03.510+0800	INFO	rpc/rpc_client.go:337	config-0-69e37497-071a-4f94-9564-0be4777c37e0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973323408_192.168.215.1_62460
2025-06-15T15:42:03.523+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:42:06.539+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:42:06.539+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:42:06.539+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6d113ee0-27d0-4593-85fb-655091f64557)
2025-06-15T15:42:06.539+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:06.539+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:06.539+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:06.539+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6d113ee0-27d0-4593-85fb-655091f64557 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:06.539+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:06.539+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:06.539+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:06.642+0800	INFO	rpc/rpc_client.go:337	config-0-6d113ee0-27d0-4593-85fb-655091f64557 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973326540_192.168.215.1_50665
2025-06-15T15:42:06.651+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:43:46.092+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:43:46.092+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:43:46.092+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9605b514-5c28-4f76-bd65-ba0fe12dec13)
2025-06-15T15:43:46.092+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:43:46.092+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:43:46.092+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:43:46.092+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9605b514-5c28-4f76-bd65-ba0fe12dec13 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:43:46.092+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:43:46.092+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:43:46.092+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:43:46.194+0800	INFO	rpc/rpc_client.go:337	config-0-9605b514-5c28-4f76-bd65-ba0fe12dec13 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973426093_192.168.215.1_58375
2025-06-15T15:43:46.207+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:51:14.656+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:51:14.657+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:51:14.657+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e9b3d61f-a4b8-47fe-9fb7-1e6f0c12a78b)
2025-06-15T15:51:14.657+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:51:14.657+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:51:14.657+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:51:14.657+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e9b3d61f-a4b8-47fe-9fb7-1e6f0c12a78b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:51:14.657+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:51:14.657+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:51:14.657+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:51:14.759+0800	INFO	rpc/rpc_client.go:337	config-0-e9b3d61f-a4b8-47fe-9fb7-1e6f0c12a78b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973874648_192.168.215.1_33559
2025-06-15T15:51:14.768+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T15:52:22.188+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-15T15:52:31.000+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T15:52:31.000+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T15:52:31.000+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-89a161bc-97fc-45df-9167-556ca8e68f10)
2025-06-15T15:52:31.000+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:52:31.000+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:52:31.000+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:52:31.000+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-89a161bc-97fc-45df-9167-556ca8e68f10 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:52:31.000+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:52:31.000+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:52:31.000+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:52:31.103+0800	INFO	rpc/rpc_client.go:337	config-0-89a161bc-97fc-45df-9167-556ca8e68f10 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973950994_192.168.215.1_65486
2025-06-15T15:52:31.110+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T16:07:07.599+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-15T16:07:11.109+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-15T16:07:11.109+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-15T16:07:11.109+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9c4acc63-7466-4aee-bcac-9c59c009b49a)
2025-06-15T16:07:11.109+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T16:07:11.109+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T16:07:11.109+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T16:07:11.109+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9c4acc63-7466-4aee-bcac-9c59c009b49a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T16:07:11.109+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T16:07:11.109+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T16:07:11.109+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T16:07:11.212+0800	INFO	rpc/rpc_client.go:337	config-0-9c4acc63-7466-4aee-bcac-9c59c009b49a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749974831112_192.168.215.1_52818
2025-06-15T16:07:11.224+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-15T16:49:00.826+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T16:16:25.176+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T16:16:25.176+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T16:16:25.176+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e5daa116-dae0-481f-b6ed-7f8d584febc7)
2025-06-16T16:16:25.176+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:16:25.176+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:16:25.176+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:16:25.176+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e5daa116-dae0-481f-b6ed-7f8d584febc7 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:16:25.176+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:16:25.176+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:16:25.176+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:16:25.279+0800	INFO	rpc/rpc_client.go:337	config-0-e5daa116-dae0-481f-b6ed-7f8d584febc7 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750061785177_192.168.215.1_40129
2025-06-16T16:16:25.293+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T16:18:46.036+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T16:18:59.292+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T16:18:59.293+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T16:18:59.293+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7ecc4563-5765-496e-86c0-bcdf1f55c763)
2025-06-16T16:18:59.293+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:18:59.293+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:18:59.293+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:18:59.293+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7ecc4563-5765-496e-86c0-bcdf1f55c763 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:18:59.293+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:18:59.293+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:18:59.293+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:18:59.396+0800	INFO	rpc/rpc_client.go:337	config-0-7ecc4563-5765-496e-86c0-bcdf1f55c763 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750061939294_192.168.215.1_56764
2025-06-16T16:18:59.411+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T16:29:07.622+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T16:29:14.398+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T16:29:14.398+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T16:29:14.398+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a5d3c7e8-f049-44ad-9bbe-74ba91d3359c)
2025-06-16T16:29:14.398+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:29:14.398+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:29:14.398+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:29:14.398+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a5d3c7e8-f049-44ad-9bbe-74ba91d3359c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:29:14.398+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:29:14.398+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:29:14.398+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:29:14.501+0800	INFO	rpc/rpc_client.go:337	config-0-a5d3c7e8-f049-44ad-9bbe-74ba91d3359c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750062554398_192.168.215.1_21564
2025-06-16T16:29:14.515+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T16:32:42.775+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T16:32:46.584+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T16:32:46.585+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T16:32:46.585+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6d853f7e-d21d-4e8e-ae97-2d536c2763a8)
2025-06-16T16:32:46.585+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:32:46.585+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:32:46.585+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:32:46.585+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6d853f7e-d21d-4e8e-ae97-2d536c2763a8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:32:46.585+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:32:46.585+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:32:46.585+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:32:46.688+0800	INFO	rpc/rpc_client.go:337	config-0-6d853f7e-d21d-4e8e-ae97-2d536c2763a8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750062766584_192.168.215.1_25666
2025-06-16T16:32:46.700+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T17:57:28.101+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T17:57:45.791+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T17:57:45.791+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T17:57:45.791+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559)
2025-06-16T17:57:45.791+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T17:57:45.791+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T17:57:45.791+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T17:57:45.791+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T17:57:45.791+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:57:45.792+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:57:45.792+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:57:45.895+0800	INFO	rpc/rpc_client.go:337	config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750067865801_192.168.215.1_27383
2025-06-16T17:57:45.917+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T17:58:19.137+0800	ERROR	rpc/rpc_client.go:525	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded
2025-06-16T17:58:27.724+0800	INFO	rpc/rpc_client.go:511	config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 server healthy check fail, currentConnection=1750067865801_192.168.215.1_27383
2025-06-16T17:58:27.724+0800	INFO	rpc/rpc_client.go:426	config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-16T17:58:24.310+0800	ERROR	rpc/rpc_client.go:525	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded
2025-06-16T17:58:30.441+0800	INFO	rpc/rpc_client.go:511	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 server healthy check fail, currentConnection=1750067865621_192.168.215.1_22690
2025-06-16T17:58:30.441+0800	INFO	rpc/rpc_client.go:426	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:58:30.441+0800	ERROR	rpc/grpc_client.go:280	connectionId 1750067865621_192.168.215.1_22690 received error event, isRunning:false, isAbandon=false, error=EOF
2025-06-16T17:58:30.441+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:58:30.441+0800	ERROR	rpc/grpc_client.go:280	connectionId 1750067865801_192.168.215.1_27383 received error event, isRunning:false, isAbandon=false, error=EOF
2025-06-16T17:58:30.547+0800	INFO	rpc/rpc_client.go:439	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750067910452_192.168.215.1_59461
2025-06-16T17:58:30.548+0800	INFO	rpc/rpc_client.go:443	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750067865621_192.168.215.1_22690
2025-06-16T17:58:30.547+0800	INFO	rpc/rpc_client.go:439	config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750067910453_192.168.215.1_41930
2025-06-16T17:58:30.548+0800	INFO	rpc/rpc_client.go:443	config-0-b8994a54-20c2-40c3-aa07-b0e0135d1559 abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750067865801_192.168.215.1_27383
2025-06-16T17:58:30.548+0800	INFO	rpc/rpc_client.go:486	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 notify disconnected event to listeners , connectionId=1750067910452_192.168.215.1_59461
2025-06-16T17:58:30.548+0800	INFO	rpc/rpc_client.go:486	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 notify connected event to listeners , connectionId=1750067910452_192.168.215.1_59461
2025-06-16T17:58:30.548+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T17:58:43.304+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T17:58:43.308+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-16T17:58:43.309+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T17:58:43.311+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T17:58:45.212+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T17:58:45.212+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T17:58:45.212+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954)
2025-06-16T17:58:45.212+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T17:58:45.213+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T17:58:45.213+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T17:58:45.213+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T17:58:45.213+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:58:45.213+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:58:45.213+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:58:45.315+0800	INFO	rpc/rpc_client.go:337	config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750067925220_192.168.215.1_25501
2025-06-16T17:58:45.335+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T18:01:36.849+0800	INFO	rpc/grpc_client.go:271	connectionId 1750067925048_192.168.215.1_25242 request stream onCompleted, switch server
2025-06-16T18:01:36.849+0800	INFO	rpc/grpc_client.go:271	connectionId 1750067925220_192.168.215.1_25501 request stream onCompleted, switch server
2025-06-16T18:01:36.849+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:01:36.851+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=InstanceRequest, body={"requestId":"","namespace":"dev","serviceName":"dsh.svc","groupName":"DEFAULT_GROUP","module":"naming","type":"deregisterInstance","instance":{"instanceId":"","ip":"*************","port":8087,"weight":0,"healthy":false,"enabled":false,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":null,"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}}, retryTimes=0, error=client not connected, current status:UNHEALTHY
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client/naming_grpc.(*NamingGrpcProxy).requestToServer
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_grpc/naming_grpc_proxy.go:88
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client/naming_grpc.(*NamingGrpcProxy).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_grpc/naming_grpc_proxy.go:124
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client.(*NamingProxyDelegate).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_proxy_delegate.go:94
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client.(*NamingClient).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_client.go:159
github.com/gogf/gf/contrib/registry/nacos/v2.(*Registry).Deregister
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/registry/nacos/v2@v2.9.0/nacos_register.go:62
github.com/gogf/gf/v2/net/ghttp.(*Server).doServiceDeregister
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_registry.go:68
github.com/gogf/gf/v2/net/ghttp.shutdownWebServersGracefully.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_process.go:269
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).RLockFunc
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:422
github.com/gogf/gf/v2/net/ghttp.shutdownWebServersGracefully
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_process.go:266
github.com/gogf/gf/v2/net/ghttp.handleProcessSignal.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_unix.go:26
github.com/gogf/gf/v2/os/gproc.listen.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gproc/gproc_signal.go:102
github.com/gogf/gf/v2/util/gutil.Try
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/util/gutil/gutil_try_catch.go:36
github.com/gogf/gf/v2/util/gutil.TryCatch
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/util/gutil/gutil_try_catch.go:49
github.com/gogf/gf/v2/os/gproc.listen
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gproc/gproc_signal.go:100
runtime.goexit
	/Users/<USER>/go/go1.24.2/src/runtime/asm_arm64.s:1223
2025-06-16T18:01:36.860+0800	INFO	rpc/rpc_client.go:426	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-16T18:01:36.860+0800	INFO	rpc/rpc_client.go:426	config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954 try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:01:36.860+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:01:36.951+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=InstanceRequest, body={"requestId":"","namespace":"dev","serviceName":"dsh.svc","groupName":"DEFAULT_GROUP","module":"naming","type":"deregisterInstance","instance":{"instanceId":"","ip":"*************","port":8087,"weight":0,"healthy":false,"enabled":false,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":null,"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}}, retryTimes=1, error=client not connected, current status:UNHEALTHY
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client/naming_grpc.(*NamingGrpcProxy).requestToServer
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_grpc/naming_grpc_proxy.go:88
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client/naming_grpc.(*NamingGrpcProxy).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_grpc/naming_grpc_proxy.go:124
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client.(*NamingProxyDelegate).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_proxy_delegate.go:94
github.com/nacos-group/nacos-sdk-go/v2/clients/naming_client.(*NamingClient).DeregisterInstance
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/naming_client/naming_client.go:159
github.com/gogf/gf/contrib/registry/nacos/v2.(*Registry).Deregister
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/registry/nacos/v2@v2.9.0/nacos_register.go:62
github.com/gogf/gf/v2/net/ghttp.(*Server).doServiceDeregister
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_registry.go:68
github.com/gogf/gf/v2/net/ghttp.shutdownWebServersGracefully.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_process.go:269
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).RLockFunc
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:422
github.com/gogf/gf/v2/net/ghttp.shutdownWebServersGracefully
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_process.go:266
github.com/gogf/gf/v2/net/ghttp.handleProcessSignal.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/net/ghttp/ghttp_server_admin_unix.go:26
github.com/gogf/gf/v2/os/gproc.listen.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gproc/gproc_signal.go:102
github.com/gogf/gf/v2/util/gutil.Try
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/util/gutil/gutil_try_catch.go:36
github.com/gogf/gf/v2/util/gutil.TryCatch
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/util/gutil/gutil_try_catch.go:49
github.com/gogf/gf/v2/os/gproc.listen
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gproc/gproc_signal.go:100
runtime.goexit
	/Users/<USER>/go/go1.24.2/src/runtime/asm_arm64.s:1223
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:439	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750068096866_192.168.215.1_61611
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:443	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750067925048_192.168.215.1_25242
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:439	config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954 success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750068096868_192.168.215.1_55598
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:443	config-0-aefa2b8a-57e4-4b9f-96e3-5f702b1f3954 abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750067925220_192.168.215.1_25501
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:486	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 notify disconnected event to listeners , connectionId=1750068096866_192.168.215.1_61611
2025-06-16T18:01:36.970+0800	INFO	rpc/rpc_client.go:486	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 notify connected event to listeners , connectionId=1750068096866_192.168.215.1_61611
2025-06-16T18:01:36.970+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T18:01:37.059+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-16T18:01:37.061+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:01:37.063+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:01:40.174+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T18:01:40.174+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T18:01:40.174+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7e87e503-ff43-4a7e-935a-d949b449a2e8)
2025-06-16T18:01:40.174+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T18:01:40.174+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T18:01:40.174+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T18:01:40.174+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7e87e503-ff43-4a7e-935a-d949b449a2e8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T18:01:40.174+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:01:40.174+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:01:40.174+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:01:40.278+0800	INFO	rpc/rpc_client.go:337	config-0-7e87e503-ff43-4a7e-935a-d949b449a2e8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750068100177_192.168.215.1_57429
2025-06-16T18:01:40.301+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T18:08:17.054+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:08:17.060+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-16T18:08:17.062+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:08:17.064+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T18:08:27.035+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T18:08:27.035+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T18:08:27.035+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-08c4d0fa-3095-472d-9eb8-58e2a7e48de1)
2025-06-16T18:08:27.035+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T18:08:27.035+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T18:08:27.035+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T18:08:27.035+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-08c4d0fa-3095-472d-9eb8-58e2a7e48de1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T18:08:27.035+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:08:27.035+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:08:27.035+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:08:27.138+0800	INFO	rpc/rpc_client.go:337	config-0-08c4d0fa-3095-472d-9eb8-58e2a7e48de1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750068507036_192.168.215.1_58947
2025-06-16T18:08:27.154+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T20:51:24.490+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T20:51:24.491+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T20:51:24.491+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3f82fd61-b9a8-4e50-83b2-914638b57b9f)
2025-06-16T20:51:24.491+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T20:51:24.491+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T20:51:24.491+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T20:51:24.491+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3f82fd61-b9a8-4e50-83b2-914638b57b9f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T20:51:24.491+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T20:51:24.491+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T20:51:24.491+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T20:51:24.593+0800	INFO	rpc/rpc_client.go:337	config-0-3f82fd61-b9a8-4e50-83b2-914638b57b9f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750078284491_192.168.215.1_43064
2025-06-16T20:51:24.603+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-16T20:51:47.223+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-16T20:52:00.753+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-16T20:52:00.753+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-16T20:52:00.754+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4cd2e2c8-c05f-4aa5-a72f-57318fcac034)
2025-06-16T20:52:00.754+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T20:52:00.754+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T20:52:00.754+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T20:52:00.754+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4cd2e2c8-c05f-4aa5-a72f-57318fcac034 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T20:52:00.754+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T20:52:00.754+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T20:52:00.754+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T20:52:00.857+0800	INFO	rpc/rpc_client.go:337	config-0-4cd2e2c8-c05f-4aa5-a72f-57318fcac034 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750078320754_192.168.215.1_35296
2025-06-16T20:52:00.872+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T09:22:48.080+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T09:22:48.080+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T09:22:48.080+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-16082f93-7d41-4ebd-8740-19d85b27950c)
2025-06-17T09:22:48.080+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T09:22:48.080+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T09:22:48.080+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T09:22:48.080+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-16082f93-7d41-4ebd-8740-19d85b27950c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T09:22:48.081+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T09:22:48.081+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T09:22:48.081+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T09:22:48.183+0800	INFO	rpc/rpc_client.go:337	config-0-16082f93-7d41-4ebd-8740-19d85b27950c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750123368081_192.168.215.1_46655
2025-06-17T09:22:48.197+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T11:34:17.723+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T11:34:31.973+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T11:34:31.973+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T11:34:31.973+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d16ccad2-e1d0-472e-85de-de5f7b17b5ef)
2025-06-17T11:34:31.973+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T11:34:31.973+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T11:34:31.973+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T11:34:31.973+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d16ccad2-e1d0-472e-85de-de5f7b17b5ef try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T11:34:31.973+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T11:34:31.973+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T11:34:31.973+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T11:34:32.075+0800	INFO	rpc/rpc_client.go:337	config-0-d16ccad2-e1d0-472e-85de-de5f7b17b5ef success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750131271974_192.168.215.1_30533
2025-06-17T11:34:32.085+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T13:40:29.377+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T13:42:25.353+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T13:42:25.353+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T13:42:25.353+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-946c40f7-b851-47a6-b5c6-db9946b6d5a2)
2025-06-17T13:42:25.353+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:42:25.353+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:42:25.353+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:42:25.353+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-946c40f7-b851-47a6-b5c6-db9946b6d5a2 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:42:25.353+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:42:25.353+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:42:25.353+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:42:25.456+0800	INFO	rpc/rpc_client.go:337	config-0-946c40f7-b851-47a6-b5c6-db9946b6d5a2 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750138945354_192.168.215.1_46221
2025-06-17T13:42:25.464+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T13:43:03.275+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T13:43:03.276+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T13:43:03.276+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3e9794f0-2809-4b6f-8d26-7d3a4bf64294)
2025-06-17T13:43:03.276+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:43:03.276+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:43:03.276+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:43:03.276+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3e9794f0-2809-4b6f-8d26-7d3a4bf64294 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:43:03.276+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:43:03.276+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:43:03.276+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:43:03.379+0800	INFO	rpc/rpc_client.go:337	config-0-3e9794f0-2809-4b6f-8d26-7d3a4bf64294 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750138983277_192.168.215.1_47041
2025-06-17T13:43:03.388+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T13:45:27.434+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T13:45:27.435+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T13:45:27.435+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c39410c1-be7f-49d9-bebf-2d6978c0681e)
2025-06-17T13:45:27.435+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:45:27.435+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:45:27.435+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:45:27.435+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c39410c1-be7f-49d9-bebf-2d6978c0681e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:45:27.435+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:45:27.435+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:45:27.435+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:45:27.538+0800	INFO	rpc/rpc_client.go:337	config-0-c39410c1-be7f-49d9-bebf-2d6978c0681e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750139127436_192.168.215.1_52540
2025-06-17T13:45:27.552+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T13:47:45.539+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T13:47:45.540+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T13:47:45.540+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6e17d9fb-1c76-451d-98e5-316f7c0aa884)
2025-06-17T13:47:45.540+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:47:45.540+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:47:45.540+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:47:45.540+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6e17d9fb-1c76-451d-98e5-316f7c0aa884 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:47:45.540+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:47:45.540+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:47:45.540+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:47:45.643+0800	INFO	rpc/rpc_client.go:337	config-0-6e17d9fb-1c76-451d-98e5-316f7c0aa884 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750139265549_192.168.215.1_51158
2025-06-17T13:47:45.656+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T14:30:27.672+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T14:30:31.440+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T14:30:31.440+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T14:30:31.440+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-528f13f5-27f5-4e9f-ae57-fe700318d219)
2025-06-17T14:30:31.440+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:30:31.440+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:30:31.440+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:30:31.440+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-528f13f5-27f5-4e9f-ae57-fe700318d219 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:30:31.440+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:30:31.440+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:30:31.440+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:30:31.543+0800	INFO	rpc/rpc_client.go:337	config-0-528f13f5-27f5-4e9f-ae57-fe700318d219 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750141831441_192.168.215.1_38501
2025-06-17T14:30:31.553+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T14:43:09.558+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T14:43:16.432+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T14:43:16.432+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T14:43:16.432+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-87152d22-12a8-4aa0-9905-0359966f05c1)
2025-06-17T14:43:16.432+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:43:16.432+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:43:16.432+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:43:16.432+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-87152d22-12a8-4aa0-9905-0359966f05c1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:43:16.432+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:43:16.432+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:43:16.432+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:43:16.534+0800	INFO	rpc/rpc_client.go:337	config-0-87152d22-12a8-4aa0-9905-0359966f05c1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750142596433_192.168.215.1_26003
2025-06-17T14:43:16.547+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T14:49:35.669+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T14:49:35.669+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T14:49:35.669+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d90de51a-edcc-4b00-8a2e-ae88d610e4f0)
2025-06-17T14:49:35.669+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:49:35.669+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:49:35.669+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:49:35.669+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d90de51a-edcc-4b00-8a2e-ae88d610e4f0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:49:35.669+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:49:35.669+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:49:35.669+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:49:35.771+0800	INFO	rpc/rpc_client.go:337	config-0-d90de51a-edcc-4b00-8a2e-ae88d610e4f0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750142975669_192.168.215.1_29757
2025-06-17T14:49:35.779+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T14:54:17.252+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T14:54:17.252+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T14:54:17.252+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-868b1982-08b9-4812-b456-3ecc803dd31c)
2025-06-17T14:54:17.252+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:54:17.252+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:54:17.252+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:54:17.252+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-868b1982-08b9-4812-b456-3ecc803dd31c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:54:17.252+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:54:17.252+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:54:17.252+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:54:17.355+0800	INFO	rpc/rpc_client.go:337	config-0-868b1982-08b9-4812-b456-3ecc803dd31c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750143257253_192.168.215.1_42277
2025-06-17T14:54:17.365+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T14:58:39.643+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T14:58:48.995+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T14:58:48.995+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T14:58:48.995+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-afe43aac-75b8-426a-ad96-e9ce64d6f07a)
2025-06-17T14:58:48.995+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:58:48.995+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:58:48.995+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:58:48.995+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-afe43aac-75b8-426a-ad96-e9ce64d6f07a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:58:48.995+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:58:48.995+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:58:48.995+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:58:49.098+0800	INFO	rpc/rpc_client.go:337	config-0-afe43aac-75b8-426a-ad96-e9ce64d6f07a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750143528996_192.168.215.1_62838
2025-06-17T14:58:49.106+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:11:00.560+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:11:00.560+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:11:00.560+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-73f51ddd-5336-4ac1-ac6d-3aba5138bc09)
2025-06-17T15:11:00.560+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:11:00.560+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:11:00.560+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:11:00.560+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-73f51ddd-5336-4ac1-ac6d-3aba5138bc09 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:11:00.560+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:11:00.560+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:11:00.560+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:11:00.663+0800	INFO	rpc/rpc_client.go:337	config-0-73f51ddd-5336-4ac1-ac6d-3aba5138bc09 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750144260577_192.168.215.1_57101
2025-06-17T15:11:00.678+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:21:55.682+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:22:46.307+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:22:46.307+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:22:46.307+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b6b83395-1f9b-4f01-928a-ec0e8492c8ec)
2025-06-17T15:22:46.307+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:22:46.307+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:22:46.307+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:22:46.307+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b6b83395-1f9b-4f01-928a-ec0e8492c8ec try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:22:46.307+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:22:46.307+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:22:46.307+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:22:46.409+0800	INFO	rpc/rpc_client.go:337	config-0-b6b83395-1f9b-4f01-928a-ec0e8492c8ec success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750144966306_192.168.215.1_53334
2025-06-17T15:22:46.419+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:29:14.320+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:29:24.050+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:29:24.050+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:29:24.050+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-aa1dd53c-45a7-4d37-aebb-b67389c4da22)
2025-06-17T15:29:24.050+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:29:24.050+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:29:24.050+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:29:24.050+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-aa1dd53c-45a7-4d37-aebb-b67389c4da22 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:29:24.050+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:29:24.050+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:29:24.050+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:29:24.152+0800	INFO	rpc/rpc_client.go:337	config-0-aa1dd53c-45a7-4d37-aebb-b67389c4da22 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145364058_192.168.215.1_54410
2025-06-17T15:29:24.163+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:29:25.228+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:29:25.232+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-17T15:29:25.234+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:29:25.236+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:29:33.586+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:29:33.586+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:29:33.586+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee)
2025-06-17T15:29:33.586+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:29:33.586+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:29:33.586+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:29:33.586+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:29:33.586+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:29:33.586+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:29:33.586+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:29:33.689+0800	INFO	rpc/rpc_client.go:337	config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145373594_192.168.215.1_21473
2025-06-17T15:29:33.710+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:31:26.120+0800	ERROR	rpc/rpc_client.go:525	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded while waiting for connections to become ready
2025-06-17T15:31:26.120+0800	INFO	rpc/rpc_client.go:511	config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee server healthy check fail, currentConnection=1750145373594_192.168.215.1_21473
2025-06-17T15:31:26.120+0800	INFO	rpc/grpc_client.go:271	connectionId 1750145373421_192.168.215.1_63337 request stream onCompleted, switch server
2025-06-17T15:31:26.121+0800	ERROR	rpc/rpc_client.go:525	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded while waiting for connections to become ready
2025-06-17T15:31:26.120+0800	INFO	rpc/grpc_client.go:271	connectionId 1750145373594_192.168.215.1_21473 request stream onCompleted, switch server
2025-06-17T15:31:26.121+0800	WARN	rpc/grpc_client.go:262	connectionId 1750145373594_192.168.215.1_21473 stream client close
2025-06-17T15:31:26.121+0800	INFO	rpc/rpc_client.go:511	09aeee7f-bd32-41af-9f8c-d73ec970b61a server healthy check fail, currentConnection=1750145373421_192.168.215.1_63337
2025-06-17T15:31:26.121+0800	INFO	rpc/rpc_client.go:426	09aeee7f-bd32-41af-9f8c-d73ec970b61a try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:31:26.120+0800	INFO	rpc/rpc_client.go:426	config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:31:26.121+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:31:26.228+0800	INFO	rpc/rpc_client.go:439	09aeee7f-bd32-41af-9f8c-d73ec970b61a success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750145486126_192.168.215.1_64148
2025-06-17T15:31:26.228+0800	INFO	rpc/rpc_client.go:443	09aeee7f-bd32-41af-9f8c-d73ec970b61a abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750145373421_192.168.215.1_63337
2025-06-17T15:31:26.228+0800	INFO	rpc/rpc_client.go:439	config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750145486126_192.168.215.1_59217
2025-06-17T15:31:26.229+0800	INFO	rpc/rpc_client.go:443	config-0-4e819f8b-c9f6-4a82-a804-cf6f21ec30ee abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750145373594_192.168.215.1_21473
2025-06-17T15:31:26.229+0800	INFO	rpc/rpc_client.go:426	09aeee7f-bd32-41af-9f8c-d73ec970b61a try to re connect to a new server, server is not appointed, will choose a random server.
2025-06-17T15:31:26.229+0800	INFO	rpc/rpc_client.go:486	09aeee7f-bd32-41af-9f8c-d73ec970b61a notify disconnected event to listeners , connectionId=1750145486126_192.168.215.1_64148
2025-06-17T15:31:26.229+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:31:26.229+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:31:26.229+0800	INFO	rpc/rpc_client.go:486	09aeee7f-bd32-41af-9f8c-d73ec970b61a notify connected event to listeners , connectionId=1750145486126_192.168.215.1_64148
2025-06-17T15:31:26.229+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:31:26.229+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:31:26.333+0800	INFO	rpc/rpc_client.go:439	09aeee7f-bd32-41af-9f8c-d73ec970b61a success to connect a server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId=1750145486232_192.168.215.1_55404
2025-06-17T15:31:26.333+0800	INFO	rpc/rpc_client.go:443	09aeee7f-bd32-41af-9f8c-d73ec970b61a abandon prev connection, server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, connectionId is 1750145486126_192.168.215.1_64148
2025-06-17T15:31:26.333+0800	INFO	rpc/rpc_client.go:486	09aeee7f-bd32-41af-9f8c-d73ec970b61a notify disconnected event to listeners , connectionId=1750145486232_192.168.215.1_55404
2025-06-17T15:31:26.333+0800	INFO	rpc/rpc_client.go:486	09aeee7f-bd32-41af-9f8c-d73ec970b61a notify connected event to listeners , connectionId=1750145486232_192.168.215.1_55404
2025-06-17T15:31:26.333+0800	ERROR	rpc/grpc_client.go:280	connectionId 1750145486126_192.168.215.1_64148 received error event, isRunning:true, isAbandon=true, error=rpc error: code = Canceled desc = grpc: the client connection is closing
2025-06-17T15:31:26.334+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:31:28.749+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:31:28.751+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-17T15:31:28.752+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:31:28.753+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:36:38.729+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:36:38.729+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:36:38.729+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-40b54caf-0634-4ad7-b495-29b085c20899)
2025-06-17T15:36:38.729+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:36:38.729+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:36:38.729+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:36:38.730+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-40b54caf-0634-4ad7-b495-29b085c20899 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:36:38.730+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:36:38.730+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:36:38.730+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:36:38.833+0800	INFO	rpc/rpc_client.go:337	config-0-40b54caf-0634-4ad7-b495-29b085c20899 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145798709_192.168.215.1_56938
2025-06-17T15:36:38.843+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:38:04.418+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T15:41:50.865+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:41:50.865+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:41:50.865+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e18cbff6-2251-46c3-ab0b-14f02a7b6bfd)
2025-06-17T15:41:50.865+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:41:50.865+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:41:50.865+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:41:50.865+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e18cbff6-2251-46c3-ab0b-14f02a7b6bfd try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:41:50.865+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:41:50.865+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:41:50.865+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:41:50.968+0800	INFO	rpc/rpc_client.go:337	config-0-e18cbff6-2251-46c3-ab0b-14f02a7b6bfd success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750146110871_192.168.215.1_63239
2025-06-17T15:41:50.978+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T15:58:41.235+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T15:58:41.236+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T15:58:41.236+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9ab44834-62ee-4933-8666-aa1a293dc0bf)
2025-06-17T15:58:41.236+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:58:41.236+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:58:41.236+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:58:41.236+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9ab44834-62ee-4933-8666-aa1a293dc0bf try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:58:41.236+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:58:41.236+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:58:41.236+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:58:41.338+0800	INFO	rpc/rpc_client.go:337	config-0-9ab44834-62ee-4933-8666-aa1a293dc0bf success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147121237_192.168.215.1_30396
2025-06-17T15:58:41.348+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:04:41.109+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:04:41.109+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:04:41.109+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-659ee9cb-a198-418d-8b1a-00f72bf7de09)
2025-06-17T16:04:41.109+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:04:41.109+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:04:41.109+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:04:41.109+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-659ee9cb-a198-418d-8b1a-00f72bf7de09 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:04:41.109+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:04:41.109+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:04:41.109+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:04:41.211+0800	INFO	rpc/rpc_client.go:337	config-0-659ee9cb-a198-418d-8b1a-00f72bf7de09 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147481110_192.168.215.1_42435
2025-06-17T16:04:41.223+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:08:25.478+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:08:25.478+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:08:25.478+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-866952b2-0d10-434f-8e70-4b99d57def34)
2025-06-17T16:08:25.478+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:08:25.478+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:08:25.478+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:08:25.478+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-866952b2-0d10-434f-8e70-4b99d57def34 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:08:25.478+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:08:25.478+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:08:25.478+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:08:25.582+0800	INFO	rpc/rpc_client.go:337	config-0-866952b2-0d10-434f-8e70-4b99d57def34 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147705479_192.168.215.1_64169
2025-06-17T16:08:25.596+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:09:27.434+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:09:27.434+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:09:27.434+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-54761545-3a78-4ce3-8be6-3d85e36fe6ba)
2025-06-17T16:09:27.434+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:09:27.434+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:09:27.434+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:09:27.434+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-54761545-3a78-4ce3-8be6-3d85e36fe6ba try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:09:27.434+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:09:27.434+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:09:27.434+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:09:27.538+0800	INFO	rpc/rpc_client.go:337	config-0-54761545-3a78-4ce3-8be6-3d85e36fe6ba success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147767436_192.168.215.1_22093
2025-06-17T16:09:27.549+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:17:13.772+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T16:17:17.971+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:17:17.971+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:17:17.971+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-eef7d0ef-14f1-4639-bfbd-df095a185e65)
2025-06-17T16:17:17.971+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:17:17.971+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:17:17.971+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:17:17.971+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-eef7d0ef-14f1-4639-bfbd-df095a185e65 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:17:17.971+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:17:17.971+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:17:17.971+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:17:18.074+0800	INFO	rpc/rpc_client.go:337	config-0-eef7d0ef-14f1-4639-bfbd-df095a185e65 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148237972_192.168.215.1_41911
2025-06-17T16:17:18.088+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:23:52.507+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T16:24:01.691+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:24:01.691+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:24:01.691+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2153e008-63fa-4a3a-85be-c45304696ec5)
2025-06-17T16:24:01.691+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:24:01.691+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:24:01.691+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:24:01.691+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2153e008-63fa-4a3a-85be-c45304696ec5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:24:01.691+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:24:01.691+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:24:01.691+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:24:01.794+0800	INFO	rpc/rpc_client.go:337	config-0-2153e008-63fa-4a3a-85be-c45304696ec5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148641692_192.168.215.1_52821
2025-06-17T16:24:01.801+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:29:44.390+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T16:29:48.266+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:29:48.266+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:29:48.266+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b99e8d33-b6e5-4129-8616-be48c102ee96)
2025-06-17T16:29:48.266+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:29:48.266+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:29:48.266+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:29:48.266+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b99e8d33-b6e5-4129-8616-be48c102ee96 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:29:48.266+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:29:48.266+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:29:48.266+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:29:48.368+0800	INFO	rpc/rpc_client.go:337	config-0-b99e8d33-b6e5-4129-8616-be48c102ee96 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148988267_192.168.215.1_24123
2025-06-17T16:29:48.377+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:33:15.967+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:33:15.968+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:33:15.968+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-84cea9a1-e65f-4d93-9623-e165f3bc58df)
2025-06-17T16:33:15.968+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:33:15.968+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:33:15.968+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:33:15.968+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-84cea9a1-e65f-4d93-9623-e165f3bc58df try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:33:15.968+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:33:15.968+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:33:15.968+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:33:16.071+0800	INFO	rpc/rpc_client.go:337	config-0-84cea9a1-e65f-4d93-9623-e165f3bc58df success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750149195970_192.168.215.1_37129
2025-06-17T16:33:16.084+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:47:07.556+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T16:47:21.517+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T16:47:21.517+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T16:47:21.517+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-f2816f7c-350a-4a89-afec-57fadb9e5c78)
2025-06-17T16:47:21.517+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:47:21.517+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:47:21.517+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:47:21.517+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-f2816f7c-350a-4a89-afec-57fadb9e5c78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:47:21.517+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:47:21.517+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:47:21.517+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:47:21.620+0800	INFO	rpc/rpc_client.go:337	config-0-f2816f7c-350a-4a89-afec-57fadb9e5c78 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750150041518_192.168.215.1_40917
2025-06-17T16:47:21.629+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T16:53:13.389+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T20:31:26.093+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T20:31:26.093+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T20:31:26.093+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-72bbc708-140d-45af-b4db-a85bb2c1ee6e)
2025-06-17T20:31:26.093+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:31:26.093+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:31:26.093+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:31:26.093+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-72bbc708-140d-45af-b4db-a85bb2c1ee6e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:31:26.093+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:31:26.093+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:31:26.093+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:31:26.196+0800	INFO	rpc/rpc_client.go:337	config-0-72bbc708-140d-45af-b4db-a85bb2c1ee6e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750163486094_192.168.215.1_58197
2025-06-17T20:31:26.205+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T20:35:46.798+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T20:35:46.798+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T20:35:46.798+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-5de4e65b-db2f-4fa4-90b9-f1c69dd15fdc)
2025-06-17T20:35:46.798+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:35:46.799+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:35:46.799+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:35:46.799+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-5de4e65b-db2f-4fa4-90b9-f1c69dd15fdc try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:35:46.799+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:35:46.799+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:35:46.799+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:35:46.900+0800	INFO	rpc/rpc_client.go:337	config-0-5de4e65b-db2f-4fa4-90b9-f1c69dd15fdc success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750163746799_192.168.215.1_28426
2025-06-17T20:35:46.909+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T20:44:56.145+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T20:46:13.407+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T20:46:13.407+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T20:46:13.407+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3aed0543-4331-4d51-8820-3336fa6db87c)
2025-06-17T20:46:13.407+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:46:13.407+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:46:13.407+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:46:13.407+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3aed0543-4331-4d51-8820-3336fa6db87c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:46:13.407+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:46:13.407+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:46:13.407+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:46:13.510+0800	INFO	rpc/rpc_client.go:337	config-0-3aed0543-4331-4d51-8820-3336fa6db87c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750164373408_192.168.215.1_20133
2025-06-17T20:46:13.524+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-17T21:18:38.315+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-17T21:19:13.065+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-17T21:19:13.065+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-17T21:19:13.065+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3807b5f5-8a36-443c-b583-8fc6b1510b68)
2025-06-17T21:19:13.065+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T21:19:13.065+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T21:19:13.065+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T21:19:13.065+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3807b5f5-8a36-443c-b583-8fc6b1510b68 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T21:19:13.065+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T21:19:13.065+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T21:19:13.065+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T21:19:13.167+0800	INFO	rpc/rpc_client.go:337	config-0-3807b5f5-8a36-443c-b583-8fc6b1510b68 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750166353105_192.168.215.1_17906
2025-06-17T21:19:13.175+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T09:07:14.124+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T09:07:14.124+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T09:07:14.124+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-49d8eee8-7479-41cd-bc3f-d622d588c41b)
2025-06-18T09:07:14.124+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T09:07:14.125+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T09:07:14.125+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T09:07:14.125+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-49d8eee8-7479-41cd-bc3f-d622d588c41b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T09:07:14.125+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T09:07:14.125+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T09:07:14.125+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T09:07:14.228+0800	INFO	rpc/rpc_client.go:337	config-0-49d8eee8-7479-41cd-bc3f-d622d588c41b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750208834127_192.168.215.1_43351
2025-06-18T09:07:14.239+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T09:18:00.351+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-18T09:18:04.366+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T09:18:04.366+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T09:18:04.366+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-bda84cf2-2087-4cd5-a0ef-909130c73091)
2025-06-18T09:18:04.366+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T09:18:04.366+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T09:18:04.366+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T09:18:04.367+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-bda84cf2-2087-4cd5-a0ef-909130c73091 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T09:18:04.367+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T09:18:04.367+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T09:18:04.367+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T09:18:04.471+0800	INFO	rpc/rpc_client.go:337	config-0-bda84cf2-2087-4cd5-a0ef-909130c73091 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750209484368_192.168.215.1_19659
2025-06-18T09:18:04.485+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T10:03:37.511+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T10:03:37.511+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T10:03:37.511+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d02e9cbe-4cde-4159-9903-b3af9f8de269)
2025-06-18T10:03:37.511+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T10:03:37.511+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T10:03:37.511+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T10:03:37.511+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d02e9cbe-4cde-4159-9903-b3af9f8de269 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T10:03:37.511+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T10:03:37.511+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T10:03:37.511+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T10:03:37.614+0800	INFO	rpc/rpc_client.go:337	config-0-d02e9cbe-4cde-4159-9903-b3af9f8de269 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750212217513_192.168.215.1_44510
2025-06-18T10:03:37.626+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T10:05:21.833+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T10:05:21.833+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T10:05:21.833+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3b8d7c13-89a0-40f0-8b3e-b8ba18c29ed9)
2025-06-18T10:05:21.833+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T10:05:21.833+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T10:05:21.833+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T10:05:21.833+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3b8d7c13-89a0-40f0-8b3e-b8ba18c29ed9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T10:05:21.833+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T10:05:21.833+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T10:05:21.833+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T10:05:21.937+0800	INFO	rpc/rpc_client.go:337	config-0-3b8d7c13-89a0-40f0-8b3e-b8ba18c29ed9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750212321835_192.168.215.1_63576
2025-06-18T10:05:21.950+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:15:37.960+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:15:37.960+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:15:37.960+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-808d2adf-bf57-4dd9-aa26-9883232dd531)
2025-06-18T11:15:37.960+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:15:37.960+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:15:37.960+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:15:37.960+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-808d2adf-bf57-4dd9-aa26-9883232dd531 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:15:37.960+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:15:37.960+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:15:37.960+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:15:38.062+0800	INFO	rpc/rpc_client.go:337	config-0-808d2adf-bf57-4dd9-aa26-9883232dd531 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750216537961_192.168.215.1_47489
2025-06-18T11:15:38.072+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:16:38.245+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:16:38.245+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:16:38.245+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-457921bb-639e-4a64-b92c-a8c25a62073c)
2025-06-18T11:16:38.245+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:16:38.245+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:16:38.245+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:16:38.245+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-457921bb-639e-4a64-b92c-a8c25a62073c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:16:38.245+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:16:38.245+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:16:38.245+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:16:38.348+0800	INFO	rpc/rpc_client.go:337	config-0-457921bb-639e-4a64-b92c-a8c25a62073c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750216598246_192.168.215.1_21391
2025-06-18T11:16:38.361+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:27:10.929+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:27:10.930+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:27:10.930+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c825b477-486d-4833-a955-36ff498e1150)
2025-06-18T11:27:10.930+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:27:10.930+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:27:10.930+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:27:10.930+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c825b477-486d-4833-a955-36ff498e1150 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:27:10.930+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:27:10.930+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:27:10.930+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:27:11.032+0800	INFO	rpc/rpc_client.go:337	config-0-c825b477-486d-4833-a955-36ff498e1150 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750217230931_192.168.215.1_33844
2025-06-18T11:27:11.044+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:32:41.354+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-18T11:32:51.787+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:32:51.787+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:32:51.787+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7c76d1e9-30f8-4db8-9add-c0fe86c14038)
2025-06-18T11:32:51.787+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:32:51.787+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:32:51.787+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:32:51.787+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7c76d1e9-30f8-4db8-9add-c0fe86c14038 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:32:51.787+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:32:51.787+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:32:51.787+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:32:51.890+0800	INFO	rpc/rpc_client.go:337	config-0-7c76d1e9-30f8-4db8-9add-c0fe86c14038 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750217571788_192.168.215.1_61336
2025-06-18T11:32:51.900+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:47:06.052+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:47:06.052+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:47:06.052+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e143d3da-5499-4bc9-81ba-7100376ae74f)
2025-06-18T11:47:06.052+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:47:06.052+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:47:06.052+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:47:06.052+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e143d3da-5499-4bc9-81ba-7100376ae74f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:47:06.052+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:47:06.052+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:47:06.052+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:47:06.154+0800	INFO	rpc/rpc_client.go:337	config-0-e143d3da-5499-4bc9-81ba-7100376ae74f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750218426053_192.168.215.1_48066
2025-06-18T11:47:06.164+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T11:48:24.335+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T11:48:24.336+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T11:48:24.336+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-dbcf630f-05f1-4147-ad76-b9ef7f6af496)
2025-06-18T11:48:24.336+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:48:24.336+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:48:24.336+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:48:24.336+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-dbcf630f-05f1-4147-ad76-b9ef7f6af496 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:48:24.336+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:48:24.336+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:48:24.336+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:48:24.438+0800	INFO	rpc/rpc_client.go:337	config-0-dbcf630f-05f1-4147-ad76-b9ef7f6af496 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750218504337_192.168.215.1_39020
2025-06-18T11:48:24.448+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T12:07:21.033+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T12:07:21.033+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T12:07:21.033+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4589a9f6-1be4-4411-9da0-1baa845a3379)
2025-06-18T12:07:21.033+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T12:07:21.033+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T12:07:21.033+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T12:07:21.033+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4589a9f6-1be4-4411-9da0-1baa845a3379 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T12:07:21.033+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T12:07:21.033+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T12:07:21.033+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T12:07:21.136+0800	INFO	rpc/rpc_client.go:337	config-0-4589a9f6-1be4-4411-9da0-1baa845a3379 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750219641021_192.168.215.1_24323
2025-06-18T12:07:21.146+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T12:08:47.527+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T12:08:47.527+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T12:08:47.527+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7a5bc87a-ee20-4e22-a593-a71fac0b5803)
2025-06-18T12:08:47.527+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T12:08:47.527+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T12:08:47.527+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T12:08:47.527+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7a5bc87a-ee20-4e22-a593-a71fac0b5803 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T12:08:47.527+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T12:08:47.527+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T12:08:47.527+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T12:08:47.629+0800	INFO	rpc/rpc_client.go:337	config-0-7a5bc87a-ee20-4e22-a593-a71fac0b5803 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750219727517_192.168.215.1_17787
2025-06-18T12:08:47.638+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T12:08:49.106+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-18T12:08:49.108+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<************:8087@DEFAULT>
2025-06-18T13:56:46.740+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T13:56:46.740+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T13:56:46.740+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e8e377da-6be6-47fc-8cf2-1fabb06589b8)
2025-06-18T13:56:46.740+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T13:56:46.740+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T13:56:46.740+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T13:56:46.740+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e8e377da-6be6-47fc-8cf2-1fabb06589b8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T13:56:46.740+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T13:56:46.740+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T13:56:46.740+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T13:56:46.842+0800	INFO	rpc/rpc_client.go:337	config-0-e8e377da-6be6-47fc-8cf2-1fabb06589b8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226206741_192.168.215.1_41789
2025-06-18T13:56:46.855+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T14:01:20.289+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-18T14:01:28.480+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T14:01:28.480+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T14:01:28.480+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-61626906-2f00-418c-9caf-4711e97c1b52)
2025-06-18T14:01:28.480+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:01:28.480+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:01:28.480+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:01:28.480+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-61626906-2f00-418c-9caf-4711e97c1b52 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:01:28.480+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:01:28.480+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:01:28.480+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:01:28.581+0800	INFO	rpc/rpc_client.go:337	config-0-61626906-2f00-418c-9caf-4711e97c1b52 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226488481_192.168.215.1_36891
2025-06-18T14:01:28.591+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T14:05:25.513+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T14:05:25.513+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T14:05:25.513+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-d67c086e-20d3-4dac-9b9c-716c9f272fb4)
2025-06-18T14:05:25.513+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:05:25.513+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:05:25.513+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:05:25.513+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-d67c086e-20d3-4dac-9b9c-716c9f272fb4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:05:25.513+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:05:25.513+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:05:25.513+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:05:25.616+0800	INFO	rpc/rpc_client.go:337	config-0-d67c086e-20d3-4dac-9b9c-716c9f272fb4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226725514_192.168.215.1_30355
2025-06-18T14:05:25.628+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T14:15:14.618+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T14:15:14.618+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T14:15:14.618+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-805e7ca8-7fa0-48f1-bfa4-cdf8ee07b4d0)
2025-06-18T14:15:14.618+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:15:14.618+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:15:14.618+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:15:14.618+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-805e7ca8-7fa0-48f1-bfa4-cdf8ee07b4d0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:15:14.618+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:15:14.618+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:15:14.618+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:15:14.722+0800	INFO	rpc/rpc_client.go:337	config-0-805e7ca8-7fa0-48f1-bfa4-cdf8ee07b4d0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750227314620_192.168.215.1_36155
2025-06-18T14:15:14.737+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T14:15:16.440+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-06-18T14:22:19.004+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T14:22:19.005+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T14:22:19.005+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6f1b78aa-0baa-40e6-9a17-5c75eee8dcb4)
2025-06-18T14:22:19.005+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:22:19.005+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:22:19.005+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:22:19.005+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6f1b78aa-0baa-40e6-9a17-5c75eee8dcb4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:22:19.005+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:22:19.005+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:22:19.005+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:22:19.108+0800	INFO	rpc/rpc_client.go:337	config-0-6f1b78aa-0baa-40e6-9a17-5c75eee8dcb4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750227739007_192.168.215.1_20512
2025-06-18T14:22:19.123+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T14:35:06.490+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-18T14:35:06.491+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-18T14:35:06.491+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-1972303b-db2e-4432-ad38-b48dacd46ff5)
2025-06-18T14:35:06.491+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:35:06.491+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:35:06.491+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:35:06.491+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-1972303b-db2e-4432-ad38-b48dacd46ff5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:35:06.491+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:35:06.491+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:35:06.491+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:35:06.594+0800	INFO	rpc/rpc_client.go:337	config-0-1972303b-db2e-4432-ad38-b48dacd46ff5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750228506492_192.168.215.1_18936
2025-06-18T14:35:06.611+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<[{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-18T16:25:16.880+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<dev>,serviceName:<dsh.svc> with instance:<*************:8087@DEFAULT>
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.360+0800	ERROR	nacos_server/nacos_server.go:98	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-07-03T14:11:19.360+0800	WARN	rpc/rpc_client.go:457	2a423f74-571c-49c2-9635-eae313a053c0 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-03T14:11:19.360+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-03T14:11:19.360+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-38dfcad7-751a-4031-8146-e8cdac45ca34)
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.360+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.360+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-07-03T14:11:19.360+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.360+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.361+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-07-03T14:11:19.361+0800	INFO	rpc/rpc_client.go:426	config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.361+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:100
github.com/gogf/gf/v2/os/gcfg.(*Config).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:162
github.com/gogf/gf/v2/frame/gins.Database.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:44
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).doSetWithLockCheck
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:217
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:254
github.com/gogf/gf/v2/internal/instance.GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/internal/instance/instance.go:65
github.com/gogf/gf/v2/frame/gins.Database
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:37
github.com/gogf/gf/v2/frame/g.DB
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/g/g_object.go:87
dataSyncHub/internal/logic/settings.(*sSettings).InitializeDatabase
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:38
dataSyncHub/internal/logic/settings.New
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:29
dataSyncHub/internal/logic/settings.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:19
runtime.doInit1
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/go1.24.2/src/runtime/asm_arm64.s:1223
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.361+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.361+0800	WARN	rpc/rpc_client.go:457	config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-03T14:11:19.460+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.461+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.461+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.461+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:100
github.com/gogf/gf/v2/os/gcfg.(*Config).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:162
github.com/gogf/gf/v2/frame/gins.Database.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:44
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).doSetWithLockCheck
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:217
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:254
github.com/gogf/gf/v2/internal/instance.GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/internal/instance/instance.go:65
github.com/gogf/gf/v2/frame/gins.Database
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:37
github.com/gogf/gf/v2/frame/g.DB
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/g/g_object.go:87
dataSyncHub/internal/logic/settings.(*sSettings).InitializeDatabase
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:38
dataSyncHub/internal/logic/settings.New
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:29
dataSyncHub/internal/logic/settings.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:19
runtime.doInit1
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/go1.24.2/src/runtime/asm_arm64.s:1223
2025-07-03T14:11:19.461+0800	WARN	rpc/rpc_client.go:457	2a423f74-571c-49c2-9635-eae313a053c0 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-03T14:11:19.461+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.461+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.461+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.461+0800	WARN	rpc/rpc_client.go:457	config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-03T14:11:19.562+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:100
github.com/gogf/gf/v2/os/gcfg.(*Config).Data
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:162
github.com/gogf/gf/v2/frame/gins.Database.func1
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:44
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).doSetWithLockCheck
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:217
github.com/gogf/gf/v2/container/gmap.(*StrAnyMap).GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/container/gmap/gmap_hash_str_any_map.go:254
github.com/gogf/gf/v2/internal/instance.GetOrSetFuncLock
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/internal/instance/instance.go:65
github.com/gogf/gf/v2/frame/gins.Database
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/gins/gins_database.go:37
github.com/gogf/gf/v2/frame/g.DB
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/frame/g/g_object.go:87
dataSyncHub/internal/logic/settings.(*sSettings).InitializeDatabase
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:38
dataSyncHub/internal/logic/settings.New
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:29
dataSyncHub/internal/logic/settings.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/internal/logic/settings/settings.go:19
runtime.doInit1
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/go1.24.2/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/go1.24.2/src/runtime/asm_arm64.s:1223
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.662+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.662+0800	ERROR	config_client/config_client.go:195	get config from server error:client not connected, current status:STARTING, dataId=dsh.yaml, group=DEFAULT_GROUP, namespaceId=dev
2025-07-03T14:11:19.663+0800	WARN	rpc/rpc_client.go:457	config-0-38dfcad7-751a-4031-8146-e8cdac45ca34 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-03T14:11:19.663+0800	WARN	rpc/rpc_client.go:457	2a423f74-571c-49c2-9635-eae313a053c0 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-08T16:58:46.803+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-08T16:58:46.804+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-08T16:58:46.804+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-aabd4c70-c02c-428a-9ac3-c6893fdb1d4a)
2025-07-08T16:58:46.804+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T16:58:46.804+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T16:58:46.804+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T16:58:46.804+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-aabd4c70-c02c-428a-9ac3-c6893fdb1d4a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T16:58:46.804+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T16:58:46.804+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T16:58:46.804+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T16:58:46.907+0800	INFO	rpc/rpc_client.go:337	config-0-aabd4c70-c02c-428a-9ac3-c6893fdb1d4a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751965126806_192.168.215.1_17004
2025-07-08T17:04:36.334+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-08T17:04:36.335+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-08T17:04:36.335+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-877328a9-d4c6-4da6-853c-07a5093cf4e4)
2025-07-08T17:04:36.335+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:04:36.335+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:04:36.335+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:04:36.335+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-877328a9-d4c6-4da6-853c-07a5093cf4e4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:04:36.335+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:04:36.335+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:04:36.335+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:04:36.440+0800	INFO	rpc/rpc_client.go:337	config-0-877328a9-d4c6-4da6-853c-07a5093cf4e4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751965476327_192.168.215.1_33217
2025-07-08T17:20:11.433+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-08T17:20:11.434+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-08T17:20:11.434+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-e9c3f0f2-52e8-4fd4-bd15-3298716cc2fd)
2025-07-08T17:20:11.434+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:20:11.434+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:20:11.434+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:20:11.434+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-e9c3f0f2-52e8-4fd4-bd15-3298716cc2fd try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:20:11.434+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:20:11.434+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:20:11.434+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:20:11.537+0800	INFO	rpc/rpc_client.go:337	config-0-e9c3f0f2-52e8-4fd4-bd15-3298716cc2fd success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751966411435_192.168.215.1_17152
2025-07-08T17:37:22.181+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-08T17:37:22.182+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-08T17:37:22.182+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-f3ac04c3-8851-4c87-9275-779af175fa89)
2025-07-08T17:37:22.182+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:37:22.182+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:37:22.182+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:37:22.182+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-f3ac04c3-8851-4c87-9275-779af175fa89 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:37:22.182+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:37:22.182+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:37:22.182+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:37:22.287+0800	INFO	rpc/rpc_client.go:337	config-0-f3ac04c3-8851-4c87-9275-779af175fa89 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751967442183_192.168.215.1_56664
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.919+0800	ERROR	nacos_server/nacos_server.go:98	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-07-10T19:26:18.919+0800	WARN	rpc/rpc_client.go:457	838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-10T19:26:18.919+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/dsh.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-07-10T19:26:18.919+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/dsh.yaml@@DEFAULT_GROUP@@dev_failover.
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65)
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.919+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-07-10T19:26:18.919+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.919+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.920+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-07-10T19:26:18.920+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.920+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-07-10T19:26:18.920+0800	INFO	rpc/rpc_client.go:426	config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.920+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=0, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
dataSyncHub/boot.initNacos
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:121
dataSyncHub/boot.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:23
runtime.doInit1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.920+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.920+0800	WARN	rpc/rpc_client.go:457	config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect server, after trying 1 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:19.020+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:19.020+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=1, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
dataSyncHub/boot.initNacos
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:121
dataSyncHub/boot.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:23
runtime.doInit1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223
2025-07-10T19:26:19.021+0800	WARN	rpc/rpc_client.go:457	838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-10T19:26:19.021+0800	WARN	rpc/rpc_client.go:457	config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect server, after trying 2 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-10T19:26:19.121+0800	ERROR	rpc/rpc_client.go:629	Send request fail, request=ConfigQueryRequest, body={"requestId":"","group":"DEFAULT_GROUP","dataId":"dsh.yaml","tenant":"dev","module":"config","tag":""}, retryTimes=2, error=client not connected, current status:STARTING
github.com/nacos-group/nacos-sdk-go/v2/common/remote/rpc.(*RpcClient).Request
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/common/remote/rpc/rpc_client.go:591
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).requestProxy
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:64
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigProxy).queryConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_proxy.go:116
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).getConfigInner
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:192
github.com/nacos-group/nacos-sdk-go/v2/clients/config_client.(*ConfigClient).GetConfig
	/Users/<USER>/go/pkg/mod/github.com/nacos-group/nacos-sdk-go/v2@v2.2.7/clients/config_client/config_client.go:160
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).updateLocalValue
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:108
github.com/gogf/gf/contrib/config/nacos/v2.(*Client).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/contrib/config/nacos/v2@v2.9.0/nacos.go:88
github.com/gogf/gf/v2/os/gcfg.(*Config).Get
	/Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.9.0/os/gcfg/gcfg.go:103
dataSyncHub/boot.initNacos
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:121
dataSyncHub/boot.init.0
	/Users/<USER>/Source/Ai app/dataSyncHub/boot/boot.go:23
runtime.doInit1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7350
runtime.doInit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:7317
runtime.main
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/proc.go:254
runtime.goexit
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/asm_arm64.s:1223
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:19.222+0800	ERROR	config_client/config_client.go:195	get config from server error:client not connected, current status:STARTING, dataId=dsh.yaml, group=DEFAULT_GROUP, namespaceId=dev
2025-07-10T19:26:19.222+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:19.222+0800	WARN	rpc/rpc_client.go:457	config-0-4639f27c-ec39-4bc5-a78b-7743060aaf65 fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
2025-07-10T19:26:19.222+0800	WARN	rpc/rpc_client.go:457	838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect server, after trying 3 times, last try server is {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}, error=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused"
