2025-06-12T12:30:35.251+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T12:30:35.353+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55701
2025-06-12T12:30:35.353+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2cc9ccec-b7ed-45b9-a63a-375ab60b3771)
2025-06-12T12:30:35.353+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T12:30:35.353+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T12:30:35.353+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T12:30:35.353+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2cc9ccec-b7ed-45b9-a63a-375ab60b3771 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T12:30:35.353+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T12:30:35.353+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T12:30:35.353+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T12:30:35.354+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T12:30:35.458+0800	INFO	rpc/rpc_client.go:337	2cc9ccec-b7ed-45b9-a63a-375ab60b3771 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749702635323_192.168.215.1_18308
2025-06-12T14:09:57.541+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:09:57.632+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55883
2025-06-12T14:09:57.632+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=95912c73-6773-4bfc-80f5-ea4e897d75b6)
2025-06-12T14:09:57.632+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:09:57.632+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:09:57.632+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:09:57.632+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 95912c73-6773-4bfc-80f5-ea4e897d75b6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:09:57.632+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:09:57.632+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:09:57.632+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:09:57.633+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:09:57.735+0800	INFO	rpc/rpc_client.go:337	95912c73-6773-4bfc-80f5-ea4e897d75b6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708597634_192.168.215.1_44066
2025-06-12T14:11:57.928+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:11:58.028+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55242
2025-06-12T14:11:58.029+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=de70c4b5-e24b-4b85-bf6a-23819274b9aa)
2025-06-12T14:11:58.029+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:11:58.029+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:11:58.029+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:11:58.029+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] de70c4b5-e24b-4b85-bf6a-23819274b9aa try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:11:58.029+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:11:58.029+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:11:58.029+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:11:58.031+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:11:58.134+0800	INFO	rpc/rpc_client.go:337	de70c4b5-e24b-4b85-bf6a-23819274b9aa success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708718032_192.168.215.1_55081
2025-06-12T14:13:18.444+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:13:18.535+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55249
2025-06-12T14:13:18.535+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9f65dc72-0b33-4607-a62e-2e5406d3ec81)
2025-06-12T14:13:18.535+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:13:18.535+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:13:18.535+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:13:18.535+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9f65dc72-0b33-4607-a62e-2e5406d3ec81 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:13:18.535+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:13:18.535+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:13:18.535+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:13:18.536+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:13:18.638+0800	INFO	rpc/rpc_client.go:337	9f65dc72-0b33-4607-a62e-2e5406d3ec81 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708798537_192.168.215.1_48491
2025-06-12T14:15:21.041+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:15:21.150+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55140
2025-06-12T14:15:21.150+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b9d622ff-c0e1-42b7-9fb4-c82af0c306ec)
2025-06-12T14:15:21.150+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:15:21.150+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:15:21.150+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:15:21.150+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b9d622ff-c0e1-42b7-9fb4-c82af0c306ec try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:15:21.150+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:15:21.150+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:15:21.150+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:15:21.150+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:15:21.253+0800	INFO	rpc/rpc_client.go:337	b9d622ff-c0e1-42b7-9fb4-c82af0c306ec success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749708921151_192.168.215.1_26758
2025-06-12T14:18:00.305+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:18:00.392+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55742
2025-06-12T14:18:00.393+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=813ebe34-96b1-4135-9a00-0bafa941c041)
2025-06-12T14:18:00.393+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:18:00.393+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:18:00.393+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:18:00.393+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 813ebe34-96b1-4135-9a00-0bafa941c041 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:18:00.393+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:18:00.393+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:18:00.393+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:18:00.393+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:18:00.496+0800	INFO	rpc/rpc_client.go:337	813ebe34-96b1-4135-9a00-0bafa941c041 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709080394_192.168.215.1_19423
2025-06-12T14:18:00.865+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:18:00.962+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55885
2025-06-12T14:18:00.962+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=201f0624-c037-48e4-a5ec-a955d8520ff9)
2025-06-12T14:18:00.962+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:18:00.962+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:18:00.962+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:18:00.962+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 201f0624-c037-48e4-a5ec-a955d8520ff9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:18:00.962+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:18:00.962+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:18:00.962+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:18:00.963+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:18:01.066+0800	INFO	rpc/rpc_client.go:337	201f0624-c037-48e4-a5ec-a955d8520ff9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709080964_192.168.215.1_20709
2025-06-12T14:21:21.664+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:21:21.769+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55175
2025-06-12T14:21:21.769+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=8329db39-dadc-40d1-8ec8-49eaf910a3ca)
2025-06-12T14:21:21.770+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:21:21.770+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:21:21.770+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:21:21.770+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 8329db39-dadc-40d1-8ec8-49eaf910a3ca try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:21:21.770+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:21:21.770+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:21:21.770+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:21:21.771+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:21:21.873+0800	INFO	rpc/rpc_client.go:337	8329db39-dadc-40d1-8ec8-49eaf910a3ca success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709281772_192.168.215.1_56857
2025-06-12T14:21:21.876+0800	INFO	rpc/rpc_client.go:486	8329db39-dadc-40d1-8ec8-49eaf910a3ca notify connected event to listeners , connectionId=1749709281772_192.168.215.1_56857
2025-06-12T14:22:56.851+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:22:56.954+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55940
2025-06-12T14:22:56.954+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=fec9dab1-a6cd-4896-97df-2d9e7789f54e)
2025-06-12T14:22:56.954+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:22:56.954+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:22:56.954+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:22:56.954+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] fec9dab1-a6cd-4896-97df-2d9e7789f54e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:22:56.954+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:22:56.954+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:22:56.954+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:22:56.955+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:22:57.057+0800	INFO	rpc/rpc_client.go:337	fec9dab1-a6cd-4896-97df-2d9e7789f54e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709376956_192.168.215.1_16513
2025-06-12T14:22:57.058+0800	INFO	rpc/rpc_client.go:486	fec9dab1-a6cd-4896-97df-2d9e7789f54e notify connected event to listeners , connectionId=1749709376956_192.168.215.1_16513
2025-06-12T14:24:55.111+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:24:55.210+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55389
2025-06-12T14:24:55.210+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=31df05d2-0023-4c98-a50a-b9f07a732645)
2025-06-12T14:24:55.210+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:24:55.210+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:24:55.210+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:24:55.210+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 31df05d2-0023-4c98-a50a-b9f07a732645 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:24:55.210+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:24:55.210+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:24:55.210+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:24:55.211+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:24:55.314+0800	INFO	rpc/rpc_client.go:337	31df05d2-0023-4c98-a50a-b9f07a732645 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709495212_192.168.215.1_42195
2025-06-12T14:24:55.314+0800	INFO	rpc/rpc_client.go:486	31df05d2-0023-4c98-a50a-b9f07a732645 notify connected event to listeners , connectionId=1749709495212_192.168.215.1_42195
2025-06-12T14:24:59.371+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:24:59.471+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55346
2025-06-12T14:24:59.471+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=54e89b62-a23a-464a-b069-cc6b27275d6c)
2025-06-12T14:24:59.471+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:24:59.471+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:24:59.471+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:24:59.472+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 54e89b62-a23a-464a-b069-cc6b27275d6c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:24:59.472+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:24:59.472+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:24:59.472+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:24:59.472+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:24:59.574+0800	INFO	rpc/rpc_client.go:337	54e89b62-a23a-464a-b069-cc6b27275d6c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749709499473_192.168.215.1_44827
2025-06-12T14:24:59.574+0800	INFO	rpc/rpc_client.go:486	54e89b62-a23a-464a-b069-cc6b27275d6c notify connected event to listeners , connectionId=1749709499473_192.168.215.1_44827
2025-06-12T14:53:19.161+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:53:19.259+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55758
2025-06-12T14:53:19.259+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=4e84c98b-f4d2-4f67-af13-ac7bdb616de1)
2025-06-12T14:53:19.259+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:53:19.259+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:53:19.260+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:53:19.260+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 4e84c98b-f4d2-4f67-af13-ac7bdb616de1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:53:19.260+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:53:19.260+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:53:19.260+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:53:19.260+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:53:19.363+0800	INFO	rpc/rpc_client.go:337	4e84c98b-f4d2-4f67-af13-ac7bdb616de1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711199261_192.168.215.1_37986
2025-06-12T14:53:19.364+0800	INFO	rpc/rpc_client.go:486	4e84c98b-f4d2-4f67-af13-ac7bdb616de1 notify connected event to listeners , connectionId=1749711199261_192.168.215.1_37986
2025-06-12T14:53:55.517+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:53:55.618+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55566
2025-06-12T14:53:55.619+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b12633c0-bcd2-4ae6-bcd8-3295f949b8d5)
2025-06-12T14:53:55.619+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:53:55.619+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:53:55.619+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:53:55.619+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b12633c0-bcd2-4ae6-bcd8-3295f949b8d5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:53:55.619+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:53:55.619+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:53:55.619+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:53:55.619+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:53:55.722+0800	INFO	rpc/rpc_client.go:337	b12633c0-bcd2-4ae6-bcd8-3295f949b8d5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711235620_192.168.215.1_59124
2025-06-12T14:53:55.723+0800	INFO	rpc/rpc_client.go:486	b12633c0-bcd2-4ae6-bcd8-3295f949b8d5 notify connected event to listeners , connectionId=1749711235620_192.168.215.1_59124
2025-06-12T14:58:34.481+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:58:34.585+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55356
2025-06-12T14:58:34.585+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=680b2cc1-de98-4ce8-bc73-b0d5de5a7d23)
2025-06-12T14:58:34.585+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:58:34.585+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:58:34.585+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:58:34.585+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 680b2cc1-de98-4ce8-bc73-b0d5de5a7d23 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:58:34.585+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:58:34.585+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:58:34.585+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:58:34.586+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:58:34.689+0800	INFO	rpc/rpc_client.go:337	680b2cc1-de98-4ce8-bc73-b0d5de5a7d23 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711514587_192.168.215.1_47263
2025-06-12T14:58:34.689+0800	INFO	rpc/rpc_client.go:486	680b2cc1-de98-4ce8-bc73-b0d5de5a7d23 notify connected event to listeners , connectionId=1749711514587_192.168.215.1_47263
2025-06-12T14:59:45.987+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T14:59:46.062+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55513
2025-06-12T14:59:46.062+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b7402526-9f95-49f7-860e-d34dbcaf9079)
2025-06-12T14:59:46.062+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T14:59:46.062+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T14:59:46.062+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T14:59:46.062+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b7402526-9f95-49f7-860e-d34dbcaf9079 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T14:59:46.062+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T14:59:46.062+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T14:59:46.062+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T14:59:46.062+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T14:59:46.165+0800	INFO	rpc/rpc_client.go:337	b7402526-9f95-49f7-860e-d34dbcaf9079 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711586063_192.168.215.1_41041
2025-06-12T14:59:46.165+0800	INFO	rpc/rpc_client.go:486	b7402526-9f95-49f7-860e-d34dbcaf9079 notify connected event to listeners , connectionId=1749711586063_192.168.215.1_41041
2025-06-12T15:00:33.497+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T15:00:33.598+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54953
2025-06-12T15:00:33.599+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=5491906e-7a6d-4450-b4b6-347bd9961557)
2025-06-12T15:00:33.599+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:00:33.599+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:00:33.599+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:00:33.600+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 5491906e-7a6d-4450-b4b6-347bd9961557 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:00:33.600+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:00:33.600+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:00:33.600+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:00:33.601+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T15:00:33.703+0800	INFO	rpc/rpc_client.go:337	5491906e-7a6d-4450-b4b6-347bd9961557 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749711633601_192.168.215.1_41336
2025-06-12T15:00:33.704+0800	INFO	rpc/rpc_client.go:486	5491906e-7a6d-4450-b4b6-347bd9961557 notify connected event to listeners , connectionId=1749711633601_192.168.215.1_41336
2025-06-12T17:44:14.557+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:44:14.660+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55759
2025-06-12T17:44:14.660+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=936c9d56-188d-4113-8795-0051e4752524)
2025-06-12T17:44:14.660+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:44:14.660+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:44:14.660+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:44:14.660+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 936c9d56-188d-4113-8795-0051e4752524 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:44:14.660+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:44:14.660+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:44:14.660+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:44:14.661+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:44:14.764+0800	INFO	rpc/rpc_client.go:337	936c9d56-188d-4113-8795-0051e4752524 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721454662_192.168.215.1_59147
2025-06-12T17:44:14.764+0800	INFO	rpc/rpc_client.go:486	936c9d56-188d-4113-8795-0051e4752524 notify connected event to listeners , connectionId=1749721454662_192.168.215.1_59147
2025-06-12T17:45:47.095+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:45:47.192+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55099
2025-06-12T17:45:47.192+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=e9594d9c-1155-4e97-9cb5-2694d55f3313)
2025-06-12T17:45:47.192+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:45:47.192+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:45:47.192+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:45:47.193+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] e9594d9c-1155-4e97-9cb5-2694d55f3313 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:45:47.193+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:45:47.193+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:45:47.193+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:45:47.193+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:45:47.296+0800	INFO	rpc/rpc_client.go:337	e9594d9c-1155-4e97-9cb5-2694d55f3313 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721547194_192.168.215.1_59462
2025-06-12T17:45:47.296+0800	INFO	rpc/rpc_client.go:486	e9594d9c-1155-4e97-9cb5-2694d55f3313 notify connected event to listeners , connectionId=1749721547194_192.168.215.1_59462
2025-06-12T17:58:09.524+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:58:09.633+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55379
2025-06-12T17:58:09.633+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9b74fde2-ff2c-45a3-9666-e2e4d0cd4d81)
2025-06-12T17:58:09.633+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:58:09.633+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:58:09.633+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:58:09.633+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9b74fde2-ff2c-45a3-9666-e2e4d0cd4d81 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:58:09.633+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:58:09.633+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:58:09.633+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:58:09.635+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:58:09.738+0800	INFO	rpc/rpc_client.go:337	9b74fde2-ff2c-45a3-9666-e2e4d0cd4d81 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722289644_192.168.215.1_43718
2025-06-12T17:58:09.738+0800	INFO	rpc/rpc_client.go:486	9b74fde2-ff2c-45a3-9666-e2e4d0cd4d81 notify connected event to listeners , connectionId=1749722289644_192.168.215.1_43718
2025-06-12T18:06:42.898+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T18:06:42.997+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55842
2025-06-12T18:06:42.997+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d12b27c7-5466-4fd7-883c-faa7a798cf3c)
2025-06-12T18:06:42.997+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:42.997+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:42.997+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:42.997+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d12b27c7-5466-4fd7-883c-faa7a798cf3c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:42.997+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:42.997+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:42.997+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:42.997+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:06:43.100+0800	INFO	rpc/rpc_client.go:337	d12b27c7-5466-4fd7-883c-faa7a798cf3c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722802998_192.168.215.1_55239
2025-06-12T18:06:43.100+0800	INFO	rpc/rpc_client.go:486	d12b27c7-5466-4fd7-883c-faa7a798cf3c notify connected event to listeners , connectionId=1749722802998_192.168.215.1_55239
2025-06-12T18:14:32.572+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T18:14:32.670+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55470
2025-06-12T18:14:32.670+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=123a8068-6d82-411c-8587-1b6108adc445)
2025-06-12T18:14:32.670+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:14:32.670+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:14:32.670+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:14:32.670+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 123a8068-6d82-411c-8587-1b6108adc445 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:14:32.670+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:14:32.670+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:14:32.670+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:14:32.671+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:14:32.775+0800	INFO	rpc/rpc_client.go:337	123a8068-6d82-411c-8587-1b6108adc445 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723272672_192.168.215.1_23808
2025-06-12T18:14:32.775+0800	INFO	rpc/rpc_client.go:486	123a8068-6d82-411c-8587-1b6108adc445 notify connected event to listeners , connectionId=1749723272672_192.168.215.1_23808
2025-06-12T18:22:33.326+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T18:22:33.426+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55825
2025-06-12T18:22:33.426+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=03fca41b-ac7b-4a7b-8c09-89210bd3c88e)
2025-06-12T18:22:33.426+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:22:33.426+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:22:33.426+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:22:33.426+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 03fca41b-ac7b-4a7b-8c09-89210bd3c88e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:22:33.426+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:22:33.426+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:22:33.426+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:22:33.427+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:22:33.528+0800	INFO	rpc/rpc_client.go:337	03fca41b-ac7b-4a7b-8c09-89210bd3c88e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723753404_192.168.215.1_31282
2025-06-12T18:22:33.529+0800	INFO	rpc/rpc_client.go:486	03fca41b-ac7b-4a7b-8c09-89210bd3c88e notify connected event to listeners , connectionId=1749723753404_192.168.215.1_31282
2025-06-13T10:33:57.133+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-13T10:33:57.236+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55596
2025-06-13T10:33:57.237+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=dde09d01-552c-4ce1-b6bc-1ff154d499c2)
2025-06-13T10:33:57.237+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:33:57.237+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:33:57.237+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:33:57.237+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] dde09d01-552c-4ce1-b6bc-1ff154d499c2 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:33:57.237+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:33:57.237+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:33:57.237+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:33:57.238+0800	INFO	util/common.go:96	Local IP:*************
2025-06-13T10:33:57.342+0800	INFO	rpc/rpc_client.go:337	dde09d01-552c-4ce1-b6bc-1ff154d499c2 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749782037239_192.168.215.1_16562
2025-06-13T10:33:57.342+0800	INFO	rpc/rpc_client.go:486	dde09d01-552c-4ce1-b6bc-1ff154d499c2 notify connected event to listeners , connectionId=1749782037239_192.168.215.1_16562
2025-06-13T10:50:46.044+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-13T10:50:46.143+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55549
2025-06-13T10:50:46.144+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d95891aa-1f4e-44b1-955f-1ab3fc40e0b1)
2025-06-13T10:50:46.144+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:50:46.144+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:50:46.144+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:50:46.144+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d95891aa-1f4e-44b1-955f-1ab3fc40e0b1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:50:46.144+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:50:46.144+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:50:46.144+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:50:46.145+0800	INFO	util/common.go:96	Local IP:**********
2025-06-13T10:50:46.247+0800	INFO	rpc/rpc_client.go:337	d95891aa-1f4e-44b1-955f-1ab3fc40e0b1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783046145_192.168.215.1_16749
2025-06-13T10:50:46.247+0800	INFO	rpc/rpc_client.go:486	d95891aa-1f4e-44b1-955f-1ab3fc40e0b1 notify connected event to listeners , connectionId=1749783046145_192.168.215.1_16749
2025-06-13T10:51:00.477+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-13T10:51:00.572+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55891
2025-06-13T10:51:00.572+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ba1e57c9-4a23-48c6-91f1-1278d3f6c7a3)
2025-06-13T10:51:00.572+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:51:00.572+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:51:00.572+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:51:00.573+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ba1e57c9-4a23-48c6-91f1-1278d3f6c7a3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:51:00.573+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:51:00.573+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:51:00.573+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:51:00.573+0800	INFO	util/common.go:96	Local IP:**********
2025-06-13T10:51:00.677+0800	INFO	rpc/rpc_client.go:337	ba1e57c9-4a23-48c6-91f1-1278d3f6c7a3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783060576_192.168.215.1_20179
2025-06-13T10:51:00.677+0800	INFO	rpc/rpc_client.go:486	ba1e57c9-4a23-48c6-91f1-1278d3f6c7a3 notify connected event to listeners , connectionId=1749783060576_192.168.215.1_20179
2025-06-13T10:51:46.904+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-13T10:51:47.004+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55896
2025-06-13T10:51:47.004+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1da77a8a-0597-4170-86b4-18c5fc87587e)
2025-06-13T10:51:47.004+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:51:47.004+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:51:47.004+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:51:47.004+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1da77a8a-0597-4170-86b4-18c5fc87587e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:51:47.004+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:51:47.004+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:51:47.004+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:51:47.006+0800	INFO	util/common.go:96	Local IP:**********
2025-06-13T10:51:47.108+0800	INFO	rpc/rpc_client.go:337	1da77a8a-0597-4170-86b4-18c5fc87587e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783107006_192.168.215.1_44393
2025-06-13T10:51:47.109+0800	INFO	rpc/rpc_client.go:486	1da77a8a-0597-4170-86b4-18c5fc87587e notify connected event to listeners , connectionId=1749783107006_192.168.215.1_44393
2025-06-13T10:54:11.085+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-13T10:54:11.186+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55204
2025-06-13T10:54:11.186+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=eb9a3379-6ef2-414e-8aaf-2d29015bfad0)
2025-06-13T10:54:11.186+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-13T10:54:11.186+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-13T10:54:11.186+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-13T10:54:11.186+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] eb9a3379-6ef2-414e-8aaf-2d29015bfad0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-13T10:54:11.186+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-13T10:54:11.186+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-13T10:54:11.186+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-13T10:54:11.187+0800	INFO	util/common.go:96	Local IP:**********
2025-06-13T10:54:11.289+0800	INFO	rpc/rpc_client.go:337	eb9a3379-6ef2-414e-8aaf-2d29015bfad0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749783251187_192.168.215.1_64489
2025-06-13T10:54:11.289+0800	INFO	rpc/rpc_client.go:486	eb9a3379-6ef2-414e-8aaf-2d29015bfad0 notify connected event to listeners , connectionId=1749783251187_192.168.215.1_64489
2025-06-14T16:10:59.208+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T16:10:59.309+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55792
2025-06-14T16:10:59.309+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c9e07f36-9724-4ac5-84f3-6189b06b0754)
2025-06-14T16:10:59.309+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T16:10:59.309+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T16:10:59.309+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T16:10:59.309+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c9e07f36-9724-4ac5-84f3-6189b06b0754 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T16:10:59.309+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T16:10:59.309+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T16:10:59.309+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T16:10:59.310+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T16:10:59.415+0800	INFO	rpc/rpc_client.go:337	c9e07f36-9724-4ac5-84f3-6189b06b0754 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749888659333_192.168.215.1_65431
2025-06-14T16:10:59.416+0800	INFO	rpc/rpc_client.go:486	c9e07f36-9724-4ac5-84f3-6189b06b0754 notify connected event to listeners , connectionId=1749888659333_192.168.215.1_65431
2025-06-14T20:44:00.865+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T20:44:00.954+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55893
2025-06-14T20:44:00.954+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c63e6407-5027-4ffa-a213-c9330588640c)
2025-06-14T20:44:00.954+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T20:44:00.954+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T20:44:00.954+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T20:44:00.954+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c63e6407-5027-4ffa-a213-c9330588640c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T20:44:00.954+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T20:44:00.954+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T20:44:00.954+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T20:44:00.954+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T20:44:01.057+0800	INFO	rpc/rpc_client.go:337	c63e6407-5027-4ffa-a213-c9330588640c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749905040955_192.168.215.1_48780
2025-06-14T20:44:01.057+0800	INFO	rpc/rpc_client.go:486	c63e6407-5027-4ffa-a213-c9330588640c notify connected event to listeners , connectionId=1749905040955_192.168.215.1_48780
2025-06-14T20:58:37.706+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T20:58:37.807+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55241
2025-06-14T20:58:37.808+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a7ac7177-de33-4e0e-b408-9e9a4d700275)
2025-06-14T20:58:37.808+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T20:58:37.808+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T20:58:37.808+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T20:58:37.808+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a7ac7177-de33-4e0e-b408-9e9a4d700275 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T20:58:37.809+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T20:58:37.809+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T20:58:37.809+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T20:58:37.810+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T20:58:37.913+0800	INFO	rpc/rpc_client.go:337	a7ac7177-de33-4e0e-b408-9e9a4d700275 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749905917874_192.168.215.1_17108
2025-06-14T20:58:37.913+0800	INFO	rpc/rpc_client.go:486	a7ac7177-de33-4e0e-b408-9e9a4d700275 notify connected event to listeners , connectionId=1749905917874_192.168.215.1_17108
2025-06-14T21:17:26.265+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T21:17:26.366+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55539
2025-06-14T21:17:26.366+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=53aee665-a114-471d-9582-45abc4b9f62b)
2025-06-14T21:17:26.366+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:17:26.367+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:17:26.367+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:17:26.367+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 53aee665-a114-471d-9582-45abc4b9f62b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:17:26.367+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:17:26.367+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:17:26.367+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:17:26.369+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T21:17:26.472+0800	INFO	rpc/rpc_client.go:337	53aee665-a114-471d-9582-45abc4b9f62b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749907046370_192.168.215.1_52418
2025-06-14T21:17:26.473+0800	INFO	rpc/rpc_client.go:486	53aee665-a114-471d-9582-45abc4b9f62b notify connected event to listeners , connectionId=1749907046370_192.168.215.1_52418
2025-06-14T21:19:08.434+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T21:19:08.531+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54959
2025-06-14T21:19:08.531+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a40a9c2c-9d57-4cd3-ae80-7387904b1aa8)
2025-06-14T21:19:08.532+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:19:08.532+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:19:08.532+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:19:08.532+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a40a9c2c-9d57-4cd3-ae80-7387904b1aa8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:19:08.532+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:19:08.532+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:19:08.532+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:19:08.533+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T21:19:08.635+0800	INFO	rpc/rpc_client.go:337	a40a9c2c-9d57-4cd3-ae80-7387904b1aa8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749907148534_192.168.215.1_25985
2025-06-14T21:19:08.636+0800	INFO	rpc/rpc_client.go:486	a40a9c2c-9d57-4cd3-ae80-7387904b1aa8 notify connected event to listeners , connectionId=1749907148534_192.168.215.1_25985
2025-06-14T21:56:32.738+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T21:56:32.838+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55071
2025-06-14T21:56:32.838+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1204a24a-008d-4f0f-94e7-00a5b3eb808c)
2025-06-14T21:56:32.838+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T21:56:32.838+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T21:56:32.838+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T21:56:32.838+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1204a24a-008d-4f0f-94e7-00a5b3eb808c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T21:56:32.838+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T21:56:32.838+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T21:56:32.838+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T21:56:32.839+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T21:56:32.942+0800	INFO	rpc/rpc_client.go:337	1204a24a-008d-4f0f-94e7-00a5b3eb808c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749909392840_192.168.215.1_53851
2025-06-14T21:56:32.942+0800	INFO	rpc/rpc_client.go:486	1204a24a-008d-4f0f-94e7-00a5b3eb808c notify connected event to listeners , connectionId=1749909392840_192.168.215.1_53851
2025-06-14T22:19:22.809+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-14T22:19:22.906+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55018
2025-06-14T22:19:22.906+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=318cc5f8-5ce4-4f53-bb6b-80266a71717a)
2025-06-14T22:19:22.906+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-14T22:19:22.906+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-14T22:19:22.906+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-14T22:19:22.906+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 318cc5f8-5ce4-4f53-bb6b-80266a71717a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-14T22:19:22.906+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-14T22:19:22.906+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-14T22:19:22.906+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-14T22:19:22.907+0800	INFO	util/common.go:96	Local IP:**********
2025-06-14T22:19:23.010+0800	INFO	rpc/rpc_client.go:337	318cc5f8-5ce4-4f53-bb6b-80266a71717a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749910762863_192.168.215.1_23514
2025-06-14T22:19:23.010+0800	INFO	rpc/rpc_client.go:486	318cc5f8-5ce4-4f53-bb6b-80266a71717a notify connected event to listeners , connectionId=1749910762863_192.168.215.1_23514
2025-06-15T06:56:59.690+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T06:56:59.787+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55795
2025-06-15T06:56:59.788+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=02b2db65-49d8-411e-8ef8-65eee7969257)
2025-06-15T06:56:59.788+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T06:56:59.788+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T06:56:59.788+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T06:56:59.788+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 02b2db65-49d8-411e-8ef8-65eee7969257 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T06:56:59.788+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T06:56:59.788+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T06:56:59.788+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T06:56:59.789+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T06:56:59.893+0800	INFO	rpc/rpc_client.go:337	02b2db65-49d8-411e-8ef8-65eee7969257 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749941819790_192.168.215.1_46139
2025-06-15T06:56:59.893+0800	INFO	rpc/rpc_client.go:486	02b2db65-49d8-411e-8ef8-65eee7969257 notify connected event to listeners , connectionId=1749941819790_192.168.215.1_46139
2025-06-15T07:55:50.058+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T07:55:50.156+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55030
2025-06-15T07:55:50.156+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ec049386-7529-4f88-a859-e5489e236fc5)
2025-06-15T07:55:50.156+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T07:55:50.156+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T07:55:50.156+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T07:55:50.156+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ec049386-7529-4f88-a859-e5489e236fc5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T07:55:50.156+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T07:55:50.156+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T07:55:50.156+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T07:55:50.157+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T07:55:50.260+0800	INFO	rpc/rpc_client.go:337	ec049386-7529-4f88-a859-e5489e236fc5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749945350153_192.168.215.1_33647
2025-06-15T07:55:50.261+0800	INFO	rpc/rpc_client.go:486	ec049386-7529-4f88-a859-e5489e236fc5 notify connected event to listeners , connectionId=1749945350153_192.168.215.1_33647
2025-06-15T15:10:40.370+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:10:40.465+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55289
2025-06-15T15:10:40.466+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=3826398a-50a4-4642-b2dd-a6729421c1fa)
2025-06-15T15:10:40.466+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:10:40.466+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:10:40.466+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:10:40.466+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 3826398a-50a4-4642-b2dd-a6729421c1fa try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:10:40.466+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:10:40.466+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:10:40.466+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:10:40.467+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:10:40.569+0800	INFO	rpc/rpc_client.go:337	3826398a-50a4-4642-b2dd-a6729421c1fa success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971440470_192.168.215.1_28483
2025-06-15T15:10:40.570+0800	INFO	rpc/rpc_client.go:486	3826398a-50a4-4642-b2dd-a6729421c1fa notify connected event to listeners , connectionId=1749971440470_192.168.215.1_28483
2025-06-15T15:15:31.690+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:15:31.789+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55373
2025-06-15T15:15:31.789+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d6c85ff0-5069-40ea-9f55-d16ec1aee671)
2025-06-15T15:15:31.789+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:15:31.789+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:15:31.789+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:15:31.789+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d6c85ff0-5069-40ea-9f55-d16ec1aee671 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:15:31.790+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:15:31.790+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:15:31.790+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:15:31.791+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:15:31.893+0800	INFO	rpc/rpc_client.go:337	d6c85ff0-5069-40ea-9f55-d16ec1aee671 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971731792_192.168.215.1_27776
2025-06-15T15:17:57.712+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:17:57.808+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54994
2025-06-15T15:17:57.808+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=4f579e6e-d2cf-485f-8425-6d5101381b1d)
2025-06-15T15:17:57.809+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:17:57.809+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:17:57.809+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:17:57.809+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 4f579e6e-d2cf-485f-8425-6d5101381b1d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:17:57.809+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:17:57.809+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:17:57.809+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:17:57.809+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:17:57.911+0800	INFO	rpc/rpc_client.go:337	4f579e6e-d2cf-485f-8425-6d5101381b1d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749971877810_192.168.215.1_61416
2025-06-15T15:17:57.911+0800	INFO	rpc/rpc_client.go:486	4f579e6e-d2cf-485f-8425-6d5101381b1d notify connected event to listeners , connectionId=1749971877810_192.168.215.1_61416
2025-06-15T15:23:12.113+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:23:12.205+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55934
2025-06-15T15:23:12.205+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=27e5d343-fe9c-4107-813e-d4c851894e9d)
2025-06-15T15:23:12.205+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:23:12.205+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:23:12.205+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:23:12.205+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 27e5d343-fe9c-4107-813e-d4c851894e9d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:23:12.205+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:23:12.205+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:23:12.205+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:23:12.205+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:23:12.307+0800	INFO	rpc/rpc_client.go:337	27e5d343-fe9c-4107-813e-d4c851894e9d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972192206_192.168.215.1_56733
2025-06-15T15:23:12.307+0800	INFO	rpc/rpc_client.go:486	27e5d343-fe9c-4107-813e-d4c851894e9d notify connected event to listeners , connectionId=1749972192206_192.168.215.1_56733
2025-06-15T15:23:58.203+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:23:58.299+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55778
2025-06-15T15:23:58.299+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d4a4fbcf-1d23-475e-833d-90301f33624a)
2025-06-15T15:23:58.299+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:23:58.299+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:23:58.299+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:23:58.299+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d4a4fbcf-1d23-475e-833d-90301f33624a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:23:58.299+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:23:58.299+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:23:58.299+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:23:58.299+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:23:58.401+0800	INFO	rpc/rpc_client.go:337	d4a4fbcf-1d23-475e-833d-90301f33624a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972238300_192.168.215.1_65323
2025-06-15T15:23:58.401+0800	INFO	rpc/rpc_client.go:486	d4a4fbcf-1d23-475e-833d-90301f33624a notify connected event to listeners , connectionId=1749972238300_192.168.215.1_65323
2025-06-15T15:25:14.783+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:25:14.862+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55597
2025-06-15T15:25:14.862+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=fdc3bbb7-238d-44e1-8c2e-39049f6aceeb)
2025-06-15T15:25:14.862+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:25:14.862+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:25:14.862+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:25:14.862+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] fdc3bbb7-238d-44e1-8c2e-39049f6aceeb try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:25:14.862+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:25:14.862+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:25:14.862+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:25:14.863+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:25:14.976+0800	INFO	rpc/rpc_client.go:337	fdc3bbb7-238d-44e1-8c2e-39049f6aceeb success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749972314863_192.168.215.1_36240
2025-06-15T15:39:55.804+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:39:55.910+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55823
2025-06-15T15:39:55.910+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=5c064c57-7017-492c-9335-eca30b3e8d00)
2025-06-15T15:39:55.910+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:39:55.910+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:39:55.910+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:39:55.910+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 5c064c57-7017-492c-9335-eca30b3e8d00 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:39:55.910+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:39:55.910+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:39:55.910+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:39:55.911+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:39:56.013+0800	INFO	rpc/rpc_client.go:337	5c064c57-7017-492c-9335-eca30b3e8d00 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973195912_192.168.215.1_17613
2025-06-15T15:39:56.014+0800	INFO	rpc/rpc_client.go:486	5c064c57-7017-492c-9335-eca30b3e8d00 notify connected event to listeners , connectionId=1749973195912_192.168.215.1_17613
2025-06-15T15:42:00.733+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:42:00.812+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55142
2025-06-15T15:42:00.812+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=7fdcfa6d-731c-48cf-a12f-105032df28d9)
2025-06-15T15:42:00.812+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:00.812+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:00.812+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:00.812+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 7fdcfa6d-731c-48cf-a12f-105032df28d9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:00.812+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:00.812+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:00.812+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:00.813+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:42:00.916+0800	INFO	rpc/rpc_client.go:337	7fdcfa6d-731c-48cf-a12f-105032df28d9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973320813_192.168.215.1_61725
2025-06-15T15:42:03.142+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:42:03.239+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55937
2025-06-15T15:42:03.239+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=0f716893-e97d-43c2-9133-0d83bb4160ae)
2025-06-15T15:42:03.239+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:03.239+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:03.239+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:03.239+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 0f716893-e97d-43c2-9133-0d83bb4160ae try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:03.239+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:03.239+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:03.239+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:03.239+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:42:03.341+0800	INFO	rpc/rpc_client.go:337	0f716893-e97d-43c2-9133-0d83bb4160ae success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973323240_192.168.215.1_26932
2025-06-15T15:42:03.341+0800	INFO	rpc/rpc_client.go:486	0f716893-e97d-43c2-9133-0d83bb4160ae notify connected event to listeners , connectionId=1749973323240_192.168.215.1_26932
2025-06-15T15:42:06.293+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:42:06.374+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55467
2025-06-15T15:42:06.374+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a06f0489-73a4-462f-b4ce-b50e98beae9c)
2025-06-15T15:42:06.374+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:42:06.374+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:42:06.374+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:42:06.374+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a06f0489-73a4-462f-b4ce-b50e98beae9c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:42:06.375+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:42:06.375+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:42:06.375+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:42:06.375+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:42:06.477+0800	INFO	rpc/rpc_client.go:337	a06f0489-73a4-462f-b4ce-b50e98beae9c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973326376_192.168.215.1_30092
2025-06-15T15:42:06.478+0800	INFO	rpc/rpc_client.go:486	a06f0489-73a4-462f-b4ce-b50e98beae9c notify connected event to listeners , connectionId=1749973326376_192.168.215.1_30092
2025-06-15T15:43:45.824+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:43:45.918+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55379
2025-06-15T15:43:45.918+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2996ed03-97f5-470f-866d-5552d1a86a83)
2025-06-15T15:43:45.918+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:43:45.918+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:43:45.918+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:43:45.918+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2996ed03-97f5-470f-866d-5552d1a86a83 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:43:45.918+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:43:45.918+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:43:45.918+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:43:45.918+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:43:46.020+0800	INFO	rpc/rpc_client.go:337	2996ed03-97f5-470f-866d-5552d1a86a83 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973425919_192.168.215.1_36006
2025-06-15T15:43:46.020+0800	INFO	rpc/rpc_client.go:486	2996ed03-97f5-470f-866d-5552d1a86a83 notify connected event to listeners , connectionId=1749973425919_192.168.215.1_36006
2025-06-15T15:51:14.395+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:51:14.487+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55125
2025-06-15T15:51:14.487+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=7f6121ba-791f-43ed-8f7a-28e0663deaee)
2025-06-15T15:51:14.487+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:51:14.487+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:51:14.487+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:51:14.487+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 7f6121ba-791f-43ed-8f7a-28e0663deaee try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:51:14.487+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:51:14.488+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:51:14.488+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:51:14.488+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:51:14.590+0800	INFO	rpc/rpc_client.go:337	7f6121ba-791f-43ed-8f7a-28e0663deaee success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973874479_192.168.215.1_49577
2025-06-15T15:51:14.590+0800	INFO	rpc/rpc_client.go:486	7f6121ba-791f-43ed-8f7a-28e0663deaee notify connected event to listeners , connectionId=1749973874479_192.168.215.1_49577
2025-06-15T15:52:30.730+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T15:52:30.827+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55543
2025-06-15T15:52:30.827+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2579647c-c39c-42e6-b9ee-3c72459dc0f4)
2025-06-15T15:52:30.827+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T15:52:30.827+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T15:52:30.827+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T15:52:30.827+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2579647c-c39c-42e6-b9ee-3c72459dc0f4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T15:52:30.827+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T15:52:30.827+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T15:52:30.827+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T15:52:30.828+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T15:52:30.930+0800	INFO	rpc/rpc_client.go:337	2579647c-c39c-42e6-b9ee-3c72459dc0f4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749973950822_192.168.215.1_16155
2025-06-15T15:52:30.930+0800	INFO	rpc/rpc_client.go:486	2579647c-c39c-42e6-b9ee-3c72459dc0f4 notify connected event to listeners , connectionId=1749973950822_192.168.215.1_16155
2025-06-15T16:07:10.834+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-15T16:07:10.936+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55019
2025-06-15T16:07:10.936+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=e3e89f20-f099-46f9-9f96-651f3bc0e665)
2025-06-15T16:07:10.936+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-15T16:07:10.936+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-15T16:07:10.936+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-15T16:07:10.936+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] e3e89f20-f099-46f9-9f96-651f3bc0e665 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-15T16:07:10.936+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-15T16:07:10.936+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-15T16:07:10.936+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-15T16:07:10.937+0800	INFO	util/common.go:96	Local IP:**********
2025-06-15T16:07:11.039+0800	INFO	rpc/rpc_client.go:337	e3e89f20-f099-46f9-9f96-651f3bc0e665 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749974830940_192.168.215.1_40069
2025-06-15T16:07:11.040+0800	INFO	rpc/rpc_client.go:486	e3e89f20-f099-46f9-9f96-651f3bc0e665 notify connected event to listeners , connectionId=1749974830940_192.168.215.1_40069
2025-06-16T16:16:24.915+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T16:16:25.014+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55861
2025-06-16T16:16:25.015+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=3c6cb680-d229-47b2-9ac7-49d9b70a6c0a)
2025-06-16T16:16:25.015+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:16:25.015+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:16:25.015+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:16:25.015+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 3c6cb680-d229-47b2-9ac7-49d9b70a6c0a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:16:25.015+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:16:25.015+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:16:25.015+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:16:25.016+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T16:16:25.118+0800	INFO	rpc/rpc_client.go:337	3c6cb680-d229-47b2-9ac7-49d9b70a6c0a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750061785017_192.168.215.1_27344
2025-06-16T16:16:25.118+0800	INFO	rpc/rpc_client.go:486	3c6cb680-d229-47b2-9ac7-49d9b70a6c0a notify connected event to listeners , connectionId=1750061785017_192.168.215.1_27344
2025-06-16T16:18:59.023+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T16:18:59.122+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55402
2025-06-16T16:18:59.122+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=8378bb26-3066-4fa8-8973-877742114ca9)
2025-06-16T16:18:59.122+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:18:59.122+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:18:59.122+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:18:59.122+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 8378bb26-3066-4fa8-8973-877742114ca9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:18:59.122+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:18:59.123+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:18:59.123+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:18:59.123+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T16:18:59.225+0800	INFO	rpc/rpc_client.go:337	8378bb26-3066-4fa8-8973-877742114ca9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750061939124_192.168.215.1_36640
2025-06-16T16:18:59.225+0800	INFO	rpc/rpc_client.go:486	8378bb26-3066-4fa8-8973-877742114ca9 notify connected event to listeners , connectionId=1750061939124_192.168.215.1_36640
2025-06-16T16:29:14.134+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T16:29:14.229+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55090
2025-06-16T16:29:14.229+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d597735b-c98a-4dc5-a48e-e38175fd98f0)
2025-06-16T16:29:14.229+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:29:14.229+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:29:14.229+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:29:14.229+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d597735b-c98a-4dc5-a48e-e38175fd98f0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:29:14.230+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:29:14.230+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:29:14.230+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:29:14.230+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T16:29:14.332+0800	INFO	rpc/rpc_client.go:337	d597735b-c98a-4dc5-a48e-e38175fd98f0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750062554229_192.168.215.1_57509
2025-06-16T16:29:14.332+0800	INFO	rpc/rpc_client.go:486	d597735b-c98a-4dc5-a48e-e38175fd98f0 notify connected event to listeners , connectionId=1750062554229_192.168.215.1_57509
2025-06-16T16:32:46.310+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T16:32:46.411+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55390
2025-06-16T16:32:46.411+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c62b9fff-5070-4dfe-91e0-022e5276d443)
2025-06-16T16:32:46.411+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T16:32:46.411+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T16:32:46.411+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T16:32:46.411+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c62b9fff-5070-4dfe-91e0-022e5276d443 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T16:32:46.411+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T16:32:46.411+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T16:32:46.411+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T16:32:46.411+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T16:32:46.513+0800	INFO	rpc/rpc_client.go:337	c62b9fff-5070-4dfe-91e0-022e5276d443 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750062766410_192.168.215.1_62961
2025-06-16T16:32:46.514+0800	INFO	rpc/rpc_client.go:486	c62b9fff-5070-4dfe-91e0-022e5276d443 notify connected event to listeners , connectionId=1750062766410_192.168.215.1_62961
2025-06-16T17:57:45.524+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T17:57:45.611+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55946
2025-06-16T17:57:45.611+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=61bcdd93-0ee2-4fa3-bd85-30bf881206e7)
2025-06-16T17:57:45.611+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T17:57:45.611+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T17:57:45.611+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T17:57:45.611+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 61bcdd93-0ee2-4fa3-bd85-30bf881206e7 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T17:57:45.611+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:57:45.611+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:57:45.611+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:57:45.613+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T17:57:45.715+0800	INFO	rpc/rpc_client.go:337	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750067865621_192.168.215.1_22690
2025-06-16T17:57:45.715+0800	INFO	rpc/rpc_client.go:486	61bcdd93-0ee2-4fa3-bd85-30bf881206e7 notify connected event to listeners , connectionId=1750067865621_192.168.215.1_22690
2025-06-16T17:58:44.950+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T17:58:45.040+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55893
2025-06-16T17:58:45.040+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=03ed1f5c-c021-45fe-82ad-6f9e4f5a7276)
2025-06-16T17:58:45.040+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T17:58:45.040+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T17:58:45.040+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T17:58:45.040+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T17:58:45.040+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T17:58:45.040+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T17:58:45.040+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T17:58:45.040+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T17:58:45.143+0800	INFO	rpc/rpc_client.go:337	03ed1f5c-c021-45fe-82ad-6f9e4f5a7276 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750067925048_192.168.215.1_25242
2025-06-16T18:01:39.905+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T18:01:39.999+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55657
2025-06-16T18:01:39.999+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=23742392-f702-4a41-8031-b7e5b2e1f4db)
2025-06-16T18:01:39.999+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T18:01:39.999+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T18:01:39.999+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T18:01:39.999+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 23742392-f702-4a41-8031-b7e5b2e1f4db try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T18:01:39.999+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:01:39.999+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:01:39.999+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:01:40.000+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T18:01:40.102+0800	INFO	rpc/rpc_client.go:337	23742392-f702-4a41-8031-b7e5b2e1f4db success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750068100002_192.168.215.1_29296
2025-06-16T18:01:40.102+0800	INFO	rpc/rpc_client.go:486	23742392-f702-4a41-8031-b7e5b2e1f4db notify connected event to listeners , connectionId=1750068100002_192.168.215.1_29296
2025-06-16T18:08:26.759+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T18:08:26.860+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55743
2025-06-16T18:08:26.860+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f2d22e35-a6c6-4e00-8a47-ddbeb9f63a3c)
2025-06-16T18:08:26.861+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T18:08:26.861+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T18:08:26.861+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T18:08:26.861+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f2d22e35-a6c6-4e00-8a47-ddbeb9f63a3c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T18:08:26.861+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T18:08:26.861+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T18:08:26.861+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T18:08:26.861+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T18:08:26.964+0800	INFO	rpc/rpc_client.go:337	f2d22e35-a6c6-4e00-8a47-ddbeb9f63a3c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750068506862_192.168.215.1_20419
2025-06-16T18:08:26.964+0800	INFO	rpc/rpc_client.go:486	f2d22e35-a6c6-4e00-8a47-ddbeb9f63a3c notify connected event to listeners , connectionId=1750068506862_192.168.215.1_20419
2025-06-16T20:51:24.220+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T20:51:24.316+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55803
2025-06-16T20:51:24.316+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=bc105044-e7bb-4c7e-bece-08367a00cecc)
2025-06-16T20:51:24.316+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T20:51:24.316+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T20:51:24.316+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T20:51:24.316+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] bc105044-e7bb-4c7e-bece-08367a00cecc try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T20:51:24.316+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T20:51:24.316+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T20:51:24.316+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T20:51:24.318+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T20:51:24.420+0800	INFO	rpc/rpc_client.go:337	bc105044-e7bb-4c7e-bece-08367a00cecc success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750078284317_192.168.215.1_30804
2025-06-16T20:51:24.420+0800	INFO	rpc/rpc_client.go:486	bc105044-e7bb-4c7e-bece-08367a00cecc notify connected event to listeners , connectionId=1750078284317_192.168.215.1_30804
2025-06-16T20:52:00.480+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-16T20:52:00.579+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55510
2025-06-16T20:52:00.579+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=72ee1af9-8f6f-48aa-9ec1-be5498dd4840)
2025-06-16T20:52:00.579+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-16T20:52:00.579+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-16T20:52:00.579+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-16T20:52:00.579+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 72ee1af9-8f6f-48aa-9ec1-be5498dd4840 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-16T20:52:00.579+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-16T20:52:00.579+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-16T20:52:00.579+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-16T20:52:00.580+0800	INFO	util/common.go:96	Local IP:*************
2025-06-16T20:52:00.682+0800	INFO	rpc/rpc_client.go:337	72ee1af9-8f6f-48aa-9ec1-be5498dd4840 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750078320579_192.168.215.1_34559
2025-06-16T20:52:00.682+0800	INFO	rpc/rpc_client.go:486	72ee1af9-8f6f-48aa-9ec1-be5498dd4840 notify connected event to listeners , connectionId=1750078320579_192.168.215.1_34559
2025-06-17T09:22:47.807+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T09:22:47.910+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55737
2025-06-17T09:22:47.910+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9af294d1-ce26-4fb6-a270-9fe4477134b2)
2025-06-17T09:22:47.910+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T09:22:47.910+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T09:22:47.910+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T09:22:47.910+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9af294d1-ce26-4fb6-a270-9fe4477134b2 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T09:22:47.910+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T09:22:47.910+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T09:22:47.910+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T09:22:47.911+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T09:22:48.014+0800	INFO	rpc/rpc_client.go:337	9af294d1-ce26-4fb6-a270-9fe4477134b2 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750123367912_192.168.215.1_38220
2025-06-17T09:22:48.015+0800	INFO	rpc/rpc_client.go:486	9af294d1-ce26-4fb6-a270-9fe4477134b2 notify connected event to listeners , connectionId=1750123367912_192.168.215.1_38220
2025-06-17T11:34:31.706+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T11:34:31.802+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55257
2025-06-17T11:34:31.802+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=331622b0-f274-44c6-bb60-28efa0316f78)
2025-06-17T11:34:31.802+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T11:34:31.803+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T11:34:31.803+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T11:34:31.803+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 331622b0-f274-44c6-bb60-28efa0316f78 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T11:34:31.803+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T11:34:31.803+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T11:34:31.803+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T11:34:31.803+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T11:34:31.905+0800	INFO	rpc/rpc_client.go:337	331622b0-f274-44c6-bb60-28efa0316f78 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750131271804_192.168.215.1_42546
2025-06-17T11:34:31.905+0800	INFO	rpc/rpc_client.go:486	331622b0-f274-44c6-bb60-28efa0316f78 notify connected event to listeners , connectionId=1750131271804_192.168.215.1_42546
2025-06-17T13:42:25.098+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T13:42:25.183+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55378
2025-06-17T13:42:25.183+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ad0e33c9-3e6d-4636-882b-05e7d881b0f1)
2025-06-17T13:42:25.183+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:42:25.183+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:42:25.183+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:42:25.183+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ad0e33c9-3e6d-4636-882b-05e7d881b0f1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:42:25.183+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:42:25.183+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:42:25.183+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:42:25.184+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T13:42:25.286+0800	INFO	rpc/rpc_client.go:337	ad0e33c9-3e6d-4636-882b-05e7d881b0f1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750138945185_192.168.215.1_22805
2025-06-17T13:42:25.286+0800	INFO	rpc/rpc_client.go:486	ad0e33c9-3e6d-4636-882b-05e7d881b0f1 notify connected event to listeners , connectionId=1750138945185_192.168.215.1_22805
2025-06-17T13:43:03.014+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T13:43:03.105+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55832
2025-06-17T13:43:03.105+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1d7a53ae-76c8-4363-8926-c7054f5ae593)
2025-06-17T13:43:03.105+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:43:03.105+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:43:03.105+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:43:03.105+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1d7a53ae-76c8-4363-8926-c7054f5ae593 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:43:03.105+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:43:03.105+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:43:03.105+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:43:03.106+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T13:43:03.208+0800	INFO	rpc/rpc_client.go:337	1d7a53ae-76c8-4363-8926-c7054f5ae593 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750138983107_192.168.215.1_16459
2025-06-17T13:43:03.208+0800	INFO	rpc/rpc_client.go:486	1d7a53ae-76c8-4363-8926-c7054f5ae593 notify connected event to listeners , connectionId=1750138983107_192.168.215.1_16459
2025-06-17T13:45:27.164+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T13:45:27.258+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55908
2025-06-17T13:45:27.259+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=579b2547-67b6-400d-a765-26a4f1edbc45)
2025-06-17T13:45:27.259+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:45:27.259+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:45:27.259+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:45:27.259+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 579b2547-67b6-400d-a765-26a4f1edbc45 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:45:27.259+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:45:27.259+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:45:27.259+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:45:27.259+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T13:45:27.362+0800	INFO	rpc/rpc_client.go:337	579b2547-67b6-400d-a765-26a4f1edbc45 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750139127260_192.168.215.1_33119
2025-06-17T13:45:27.362+0800	INFO	rpc/rpc_client.go:486	579b2547-67b6-400d-a765-26a4f1edbc45 notify connected event to listeners , connectionId=1750139127260_192.168.215.1_33119
2025-06-17T13:47:31.145+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T13:47:45.260+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T13:47:45.364+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55779
2025-06-17T13:47:45.364+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=7654cf9a-0c2e-4ac5-879a-521ac5629eaa)
2025-06-17T13:47:45.364+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T13:47:45.364+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T13:47:45.364+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T13:47:45.364+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 7654cf9a-0c2e-4ac5-879a-521ac5629eaa try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T13:47:45.364+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T13:47:45.364+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T13:47:45.364+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T13:47:45.365+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T13:47:45.468+0800	INFO	rpc/rpc_client.go:337	7654cf9a-0c2e-4ac5-879a-521ac5629eaa success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750139265374_192.168.215.1_23053
2025-06-17T13:47:45.468+0800	INFO	rpc/rpc_client.go:486	7654cf9a-0c2e-4ac5-879a-521ac5629eaa notify connected event to listeners , connectionId=1750139265374_192.168.215.1_23053
2025-06-17T14:30:31.172+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T14:30:31.267+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55372
2025-06-17T14:30:31.268+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=46a9c6c4-3fe8-4569-9af5-564ad00657c9)
2025-06-17T14:30:31.268+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:30:31.268+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:30:31.268+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:30:31.268+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 46a9c6c4-3fe8-4569-9af5-564ad00657c9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:30:31.268+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:30:31.268+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:30:31.268+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:30:31.268+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T14:30:31.371+0800	INFO	rpc/rpc_client.go:337	46a9c6c4-3fe8-4569-9af5-564ad00657c9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750141831269_192.168.215.1_65443
2025-06-17T14:30:31.371+0800	INFO	rpc/rpc_client.go:486	46a9c6c4-3fe8-4569-9af5-564ad00657c9 notify connected event to listeners , connectionId=1750141831269_192.168.215.1_65443
2025-06-17T14:43:16.165+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T14:43:16.261+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55662
2025-06-17T14:43:16.261+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=0d73fed4-5112-4442-8f9c-d1f5c9166db6)
2025-06-17T14:43:16.261+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:43:16.261+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:43:16.261+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:43:16.261+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 0d73fed4-5112-4442-8f9c-d1f5c9166db6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:43:16.261+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:43:16.261+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:43:16.261+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:43:16.262+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T14:43:16.364+0800	INFO	rpc/rpc_client.go:337	0d73fed4-5112-4442-8f9c-d1f5c9166db6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750142596263_192.168.215.1_23546
2025-06-17T14:43:16.364+0800	INFO	rpc/rpc_client.go:486	0d73fed4-5112-4442-8f9c-d1f5c9166db6 notify connected event to listeners , connectionId=1750142596263_192.168.215.1_23546
2025-06-17T14:49:35.402+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T14:49:35.497+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55322
2025-06-17T14:49:35.497+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=49fa4da3-d7e6-4b14-8703-7ccd7c7bab1f)
2025-06-17T14:49:35.497+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:49:35.497+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:49:35.497+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:49:35.497+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 49fa4da3-d7e6-4b14-8703-7ccd7c7bab1f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:49:35.497+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:49:35.497+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:49:35.497+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:49:35.498+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T14:49:35.599+0800	INFO	rpc/rpc_client.go:337	49fa4da3-d7e6-4b14-8703-7ccd7c7bab1f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750142975498_192.168.215.1_48465
2025-06-17T14:49:35.599+0800	INFO	rpc/rpc_client.go:486	49fa4da3-d7e6-4b14-8703-7ccd7c7bab1f notify connected event to listeners , connectionId=1750142975498_192.168.215.1_48465
2025-06-17T14:54:16.981+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T14:54:17.080+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55480
2025-06-17T14:54:17.081+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=03d88934-f83b-4b2c-86ae-4f012f5cb00c)
2025-06-17T14:54:17.081+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:54:17.081+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:54:17.082+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:54:17.082+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 03d88934-f83b-4b2c-86ae-4f012f5cb00c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:54:17.082+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:54:17.082+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:54:17.082+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:54:17.083+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T14:54:17.185+0800	INFO	rpc/rpc_client.go:337	03d88934-f83b-4b2c-86ae-4f012f5cb00c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750143257083_192.168.215.1_62049
2025-06-17T14:54:17.186+0800	INFO	rpc/rpc_client.go:486	03d88934-f83b-4b2c-86ae-4f012f5cb00c notify connected event to listeners , connectionId=1750143257083_192.168.215.1_62049
2025-06-17T14:58:48.724+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T14:58:48.821+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55866
2025-06-17T14:58:48.822+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b3e12f61-1321-414e-8e56-9a3f181f322a)
2025-06-17T14:58:48.822+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T14:58:48.822+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T14:58:48.822+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T14:58:48.822+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b3e12f61-1321-414e-8e56-9a3f181f322a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T14:58:48.822+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T14:58:48.822+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T14:58:48.822+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T14:58:48.823+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T14:58:48.925+0800	INFO	rpc/rpc_client.go:337	b3e12f61-1321-414e-8e56-9a3f181f322a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750143528823_192.168.215.1_63668
2025-06-17T14:58:48.925+0800	INFO	rpc/rpc_client.go:486	b3e12f61-1321-414e-8e56-9a3f181f322a notify connected event to listeners , connectionId=1750143528823_192.168.215.1_63668
2025-06-17T15:11:00.289+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:11:00.388+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55669
2025-06-17T15:11:00.389+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ffb79236-ef2d-4c31-bf16-fbf73aa00ceb)
2025-06-17T15:11:00.389+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:11:00.389+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:11:00.389+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:11:00.389+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ffb79236-ef2d-4c31-bf16-fbf73aa00ceb try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:11:00.389+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:11:00.389+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:11:00.389+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:11:00.389+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:11:00.492+0800	INFO	rpc/rpc_client.go:337	ffb79236-ef2d-4c31-bf16-fbf73aa00ceb success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750144260406_192.168.215.1_27117
2025-06-17T15:11:00.492+0800	INFO	rpc/rpc_client.go:486	ffb79236-ef2d-4c31-bf16-fbf73aa00ceb notify connected event to listeners , connectionId=1750144260406_192.168.215.1_27117
2025-06-17T15:22:46.033+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:22:46.133+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55337
2025-06-17T15:22:46.133+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=34b8b9f3-9179-42e7-abf2-57db1016e53c)
2025-06-17T15:22:46.133+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:22:46.133+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:22:46.133+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:22:46.133+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 34b8b9f3-9179-42e7-abf2-57db1016e53c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:22:46.133+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:22:46.133+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:22:46.133+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:22:46.134+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:22:46.237+0800	INFO	rpc/rpc_client.go:337	34b8b9f3-9179-42e7-abf2-57db1016e53c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750144966133_192.168.215.1_44546
2025-06-17T15:29:23.788+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:29:23.878+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55631
2025-06-17T15:29:23.878+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f1af57ab-56a9-45ef-9171-f0b0e925d04d)
2025-06-17T15:29:23.878+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:29:23.878+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:29:23.878+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:29:23.878+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f1af57ab-56a9-45ef-9171-f0b0e925d04d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:29:23.878+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:29:23.878+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:29:23.878+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:29:23.879+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:29:23.980+0800	INFO	rpc/rpc_client.go:337	f1af57ab-56a9-45ef-9171-f0b0e925d04d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145363886_192.168.215.1_43101
2025-06-17T15:29:23.980+0800	INFO	rpc/rpc_client.go:486	f1af57ab-56a9-45ef-9171-f0b0e925d04d notify connected event to listeners , connectionId=1750145363886_192.168.215.1_43101
2025-06-17T15:29:33.325+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:29:33.413+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55921
2025-06-17T15:29:33.413+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=09aeee7f-bd32-41af-9f8c-d73ec970b61a)
2025-06-17T15:29:33.413+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:29:33.413+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:29:33.413+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:29:33.413+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 09aeee7f-bd32-41af-9f8c-d73ec970b61a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:29:33.413+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:29:33.413+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:29:33.413+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:29:33.414+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:29:33.516+0800	INFO	rpc/rpc_client.go:337	09aeee7f-bd32-41af-9f8c-d73ec970b61a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145373421_192.168.215.1_63337
2025-06-17T15:29:33.516+0800	INFO	rpc/rpc_client.go:486	09aeee7f-bd32-41af-9f8c-d73ec970b61a notify connected event to listeners , connectionId=1750145373421_192.168.215.1_63337
2025-06-17T15:36:38.457+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:36:38.556+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55426
2025-06-17T15:36:38.556+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=450e5864-94f0-4a6a-b504-a6923c9363a3)
2025-06-17T15:36:38.557+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:36:38.557+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:36:38.557+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:36:38.557+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 450e5864-94f0-4a6a-b504-a6923c9363a3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:36:38.557+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:36:38.557+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:36:38.557+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:36:38.557+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:36:38.660+0800	INFO	rpc/rpc_client.go:337	450e5864-94f0-4a6a-b504-a6923c9363a3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750145798536_192.168.215.1_50566
2025-06-17T15:36:38.660+0800	INFO	rpc/rpc_client.go:486	450e5864-94f0-4a6a-b504-a6923c9363a3 notify connected event to listeners , connectionId=1750145798536_192.168.215.1_50566
2025-06-17T15:41:50.603+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:41:50.700+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55196
2025-06-17T15:41:50.700+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=372e0256-2d15-4e81-bcba-af8c70f7021a)
2025-06-17T15:41:50.700+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:41:50.700+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:41:50.700+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:41:50.700+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 372e0256-2d15-4e81-bcba-af8c70f7021a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:41:50.700+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:41:50.700+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:41:50.700+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:41:50.701+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:41:50.804+0800	INFO	rpc/rpc_client.go:337	372e0256-2d15-4e81-bcba-af8c70f7021a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750146110707_192.168.215.1_28431
2025-06-17T15:41:50.804+0800	INFO	rpc/rpc_client.go:486	372e0256-2d15-4e81-bcba-af8c70f7021a notify connected event to listeners , connectionId=1750146110707_192.168.215.1_28431
2025-06-17T15:58:40.970+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T15:58:41.062+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55190
2025-06-17T15:58:41.062+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=360041cc-6487-4cc4-b03d-137bda39adf3)
2025-06-17T15:58:41.062+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T15:58:41.062+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T15:58:41.062+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T15:58:41.062+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 360041cc-6487-4cc4-b03d-137bda39adf3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T15:58:41.062+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T15:58:41.062+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T15:58:41.062+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T15:58:41.063+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T15:58:41.165+0800	INFO	rpc/rpc_client.go:337	360041cc-6487-4cc4-b03d-137bda39adf3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147121064_192.168.215.1_60782
2025-06-17T15:58:41.165+0800	INFO	rpc/rpc_client.go:486	360041cc-6487-4cc4-b03d-137bda39adf3 notify connected event to listeners , connectionId=1750147121064_192.168.215.1_60782
2025-06-17T16:04:40.834+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:04:40.934+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55089
2025-06-17T16:04:40.934+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=12c2ca88-4f89-4a4e-b497-7f4b29d4523a)
2025-06-17T16:04:40.934+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:04:40.934+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:04:40.934+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:04:40.934+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 12c2ca88-4f89-4a4e-b497-7f4b29d4523a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:04:40.934+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:04:40.934+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:04:40.934+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:04:40.935+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:04:41.039+0800	INFO	rpc/rpc_client.go:337	12c2ca88-4f89-4a4e-b497-7f4b29d4523a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147480937_192.168.215.1_62132
2025-06-17T16:04:41.040+0800	INFO	rpc/rpc_client.go:486	12c2ca88-4f89-4a4e-b497-7f4b29d4523a notify connected event to listeners , connectionId=1750147480937_192.168.215.1_62132
2025-06-17T16:08:25.204+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:08:25.303+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55061
2025-06-17T16:08:25.303+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f189a17c-05c5-4ade-a329-26c25326bd63)
2025-06-17T16:08:25.303+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:08:25.303+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:08:25.303+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:08:25.303+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f189a17c-05c5-4ade-a329-26c25326bd63 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:08:25.303+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:08:25.303+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:08:25.303+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:08:25.304+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:08:25.407+0800	INFO	rpc/rpc_client.go:337	f189a17c-05c5-4ade-a329-26c25326bd63 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147705305_192.168.215.1_38065
2025-06-17T16:08:25.407+0800	INFO	rpc/rpc_client.go:486	f189a17c-05c5-4ade-a329-26c25326bd63 notify connected event to listeners , connectionId=1750147705305_192.168.215.1_38065
2025-06-17T16:09:27.161+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:09:27.259+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55723
2025-06-17T16:09:27.259+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ddc9af1c-2265-4ee9-952a-fb17131ba5b0)
2025-06-17T16:09:27.259+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:09:27.259+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:09:27.259+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:09:27.259+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ddc9af1c-2265-4ee9-952a-fb17131ba5b0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:09:27.259+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:09:27.259+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:09:27.259+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:09:27.261+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:09:27.363+0800	INFO	rpc/rpc_client.go:337	ddc9af1c-2265-4ee9-952a-fb17131ba5b0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750147767262_192.168.215.1_25682
2025-06-17T16:09:27.364+0800	INFO	rpc/rpc_client.go:486	ddc9af1c-2265-4ee9-952a-fb17131ba5b0 notify connected event to listeners , connectionId=1750147767262_192.168.215.1_25682
2025-06-17T16:17:17.703+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:17:17.800+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55584
2025-06-17T16:17:17.800+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=4604ee1a-f45f-4492-8183-c09078aa4357)
2025-06-17T16:17:17.800+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:17:17.800+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:17:17.800+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:17:17.800+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 4604ee1a-f45f-4492-8183-c09078aa4357 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:17:17.800+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:17:17.800+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:17:17.800+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:17:17.801+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:17:17.902+0800	INFO	rpc/rpc_client.go:337	4604ee1a-f45f-4492-8183-c09078aa4357 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148237802_192.168.215.1_38416
2025-06-17T16:17:17.903+0800	INFO	rpc/rpc_client.go:486	4604ee1a-f45f-4492-8183-c09078aa4357 notify connected event to listeners , connectionId=1750148237802_192.168.215.1_38416
2025-06-17T16:24:01.425+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:24:01.524+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55376
2025-06-17T16:24:01.524+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=dcf89b72-dd41-4f39-875b-63c586812fa1)
2025-06-17T16:24:01.524+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:24:01.524+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:24:01.524+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:24:01.524+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] dcf89b72-dd41-4f39-875b-63c586812fa1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:24:01.524+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:24:01.524+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:24:01.524+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:24:01.525+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:24:01.627+0800	INFO	rpc/rpc_client.go:337	dcf89b72-dd41-4f39-875b-63c586812fa1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148641526_192.168.215.1_23020
2025-06-17T16:24:01.627+0800	INFO	rpc/rpc_client.go:486	dcf89b72-dd41-4f39-875b-63c586812fa1 notify connected event to listeners , connectionId=1750148641526_192.168.215.1_23020
2025-06-17T16:29:48.019+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:29:48.102+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55615
2025-06-17T16:29:48.102+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=83b4b3ef-0cab-4a33-9f3a-7fb3c16d03d5)
2025-06-17T16:29:48.102+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:29:48.102+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:29:48.102+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:29:48.102+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 83b4b3ef-0cab-4a33-9f3a-7fb3c16d03d5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:29:48.102+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:29:48.102+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:29:48.102+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:29:48.103+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:29:48.204+0800	INFO	rpc/rpc_client.go:337	83b4b3ef-0cab-4a33-9f3a-7fb3c16d03d5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750148988103_192.168.215.1_41746
2025-06-17T16:29:48.204+0800	INFO	rpc/rpc_client.go:486	83b4b3ef-0cab-4a33-9f3a-7fb3c16d03d5 notify connected event to listeners , connectionId=1750148988103_192.168.215.1_41746
2025-06-17T16:33:15.697+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:33:15.795+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55250
2025-06-17T16:33:15.795+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=0dc70fb3-86d3-4942-bff2-093f81d6acad)
2025-06-17T16:33:15.796+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:33:15.796+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:33:15.796+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:33:15.796+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 0dc70fb3-86d3-4942-bff2-093f81d6acad try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:33:15.796+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:33:15.796+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:33:15.796+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:33:15.797+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:33:15.899+0800	INFO	rpc/rpc_client.go:337	0dc70fb3-86d3-4942-bff2-093f81d6acad success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750149195798_192.168.215.1_61638
2025-06-17T16:33:15.899+0800	INFO	rpc/rpc_client.go:486	0dc70fb3-86d3-4942-bff2-093f81d6acad notify connected event to listeners , connectionId=1750149195798_192.168.215.1_61638
2025-06-17T16:47:21.248+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T16:47:21.341+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55552
2025-06-17T16:47:21.341+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2b1e6c7d-101c-4da1-9379-681088672647)
2025-06-17T16:47:21.341+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T16:47:21.341+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T16:47:21.341+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T16:47:21.341+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2b1e6c7d-101c-4da1-9379-681088672647 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T16:47:21.341+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T16:47:21.341+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T16:47:21.341+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T16:47:21.342+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T16:47:21.443+0800	INFO	rpc/rpc_client.go:337	2b1e6c7d-101c-4da1-9379-681088672647 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750150041342_192.168.215.1_19817
2025-06-17T16:47:21.443+0800	INFO	rpc/rpc_client.go:486	2b1e6c7d-101c-4da1-9379-681088672647 notify connected event to listeners , connectionId=1750150041342_192.168.215.1_19817
2025-06-17T20:31:25.842+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T20:31:25.930+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55063
2025-06-17T20:31:25.930+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=dbeac832-103f-4cec-849c-3d1e9c462c8e)
2025-06-17T20:31:25.930+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:31:25.930+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:31:25.930+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:31:25.930+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] dbeac832-103f-4cec-849c-3d1e9c462c8e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:31:25.930+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:31:25.930+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:31:25.930+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:31:25.930+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T20:31:26.033+0800	INFO	rpc/rpc_client.go:337	dbeac832-103f-4cec-849c-3d1e9c462c8e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750163485931_192.168.215.1_63514
2025-06-17T20:31:26.033+0800	INFO	rpc/rpc_client.go:486	dbeac832-103f-4cec-849c-3d1e9c462c8e notify connected event to listeners , connectionId=1750163485931_192.168.215.1_63514
2025-06-17T20:35:46.535+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T20:35:46.632+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55009
2025-06-17T20:35:46.632+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1f482e12-7ee1-40fd-9448-a1d071b964cc)
2025-06-17T20:35:46.632+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:35:46.632+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:35:46.632+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:35:46.632+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1f482e12-7ee1-40fd-9448-a1d071b964cc try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:35:46.632+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:35:46.632+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:35:46.632+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:35:46.633+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T20:35:46.734+0800	INFO	rpc/rpc_client.go:337	1f482e12-7ee1-40fd-9448-a1d071b964cc success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750163746632_192.168.215.1_58076
2025-06-17T20:35:46.734+0800	INFO	rpc/rpc_client.go:486	1f482e12-7ee1-40fd-9448-a1d071b964cc notify connected event to listeners , connectionId=1750163746632_192.168.215.1_58076
2025-06-17T20:46:13.138+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T20:46:13.236+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55707
2025-06-17T20:46:13.237+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=05041f8f-2e02-42d0-8e14-ffb93dd15c57)
2025-06-17T20:46:13.237+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T20:46:13.237+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T20:46:13.237+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T20:46:13.237+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 05041f8f-2e02-42d0-8e14-ffb93dd15c57 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T20:46:13.237+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T20:46:13.237+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T20:46:13.237+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T20:46:13.237+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T20:46:13.340+0800	INFO	rpc/rpc_client.go:337	05041f8f-2e02-42d0-8e14-ffb93dd15c57 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750164373239_192.168.215.1_62425
2025-06-17T20:46:13.340+0800	INFO	rpc/rpc_client.go:486	05041f8f-2e02-42d0-8e14-ffb93dd15c57 notify connected event to listeners , connectionId=1750164373239_192.168.215.1_62425
2025-06-17T21:19:12.802+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-17T21:19:12.895+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55937
2025-06-17T21:19:12.895+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ae1af9e5-5b85-44e6-a494-5fec510550f5)
2025-06-17T21:19:12.895+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-17T21:19:12.895+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-17T21:19:12.895+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-17T21:19:12.895+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ae1af9e5-5b85-44e6-a494-5fec510550f5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-17T21:19:12.895+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-17T21:19:12.895+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-17T21:19:12.895+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-17T21:19:12.896+0800	INFO	util/common.go:96	Local IP:*************
2025-06-17T21:19:12.999+0800	INFO	rpc/rpc_client.go:337	ae1af9e5-5b85-44e6-a494-5fec510550f5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750166352937_192.168.215.1_60094
2025-06-17T21:19:12.999+0800	INFO	rpc/rpc_client.go:486	ae1af9e5-5b85-44e6-a494-5fec510550f5 notify connected event to listeners , connectionId=1750166352937_192.168.215.1_60094
2025-06-18T09:07:13.850+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T09:07:13.953+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55141
2025-06-18T09:07:13.953+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=478db67e-a622-4b9f-8b8a-e384b59581eb)
2025-06-18T09:07:13.953+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T09:07:13.953+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T09:07:13.954+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T09:07:13.954+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 478db67e-a622-4b9f-8b8a-e384b59581eb try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T09:07:13.954+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T09:07:13.954+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T09:07:13.954+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T09:07:13.955+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T09:07:14.058+0800	INFO	rpc/rpc_client.go:337	478db67e-a622-4b9f-8b8a-e384b59581eb success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750208833957_192.168.215.1_53066
2025-06-18T09:18:04.096+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T09:18:04.196+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55151
2025-06-18T09:18:04.196+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1ebba82a-abd4-4d20-9e06-0cc36c1f8be3)
2025-06-18T09:18:04.196+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T09:18:04.196+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T09:18:04.196+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T09:18:04.196+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1ebba82a-abd4-4d20-9e06-0cc36c1f8be3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T09:18:04.196+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T09:18:04.196+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T09:18:04.196+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T09:18:04.196+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T09:18:04.299+0800	INFO	rpc/rpc_client.go:337	1ebba82a-abd4-4d20-9e06-0cc36c1f8be3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750209484197_192.168.215.1_40906
2025-06-18T10:03:37.240+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T10:03:37.337+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55396
2025-06-18T10:03:37.338+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a96d9ced-eaaa-4975-8f46-e816c94e1762)
2025-06-18T10:03:37.338+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T10:03:37.338+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T10:03:37.338+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T10:03:37.338+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a96d9ced-eaaa-4975-8f46-e816c94e1762 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T10:03:37.338+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T10:03:37.338+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T10:03:37.338+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T10:03:37.339+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T10:03:37.444+0800	INFO	rpc/rpc_client.go:337	a96d9ced-eaaa-4975-8f46-e816c94e1762 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750212217341_192.168.215.1_49338
2025-06-18T10:03:37.444+0800	INFO	rpc/rpc_client.go:486	a96d9ced-eaaa-4975-8f46-e816c94e1762 notify connected event to listeners , connectionId=1750212217341_192.168.215.1_49338
2025-06-18T10:05:21.556+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T10:05:21.646+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55280
2025-06-18T10:05:21.646+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d9967447-b24f-4b53-a393-868bfcae01e9)
2025-06-18T10:05:21.647+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T10:05:21.647+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T10:05:21.647+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T10:05:21.647+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d9967447-b24f-4b53-a393-868bfcae01e9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T10:05:21.647+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T10:05:21.647+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T10:05:21.647+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T10:05:21.648+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T10:05:21.751+0800	INFO	rpc/rpc_client.go:337	d9967447-b24f-4b53-a393-868bfcae01e9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750212321650_192.168.215.1_27723
2025-06-18T10:05:21.751+0800	INFO	rpc/rpc_client.go:486	d9967447-b24f-4b53-a393-868bfcae01e9 notify connected event to listeners , connectionId=1750212321650_192.168.215.1_27723
2025-06-18T11:15:37.689+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:15:37.788+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55519
2025-06-18T11:15:37.788+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=be76fec5-7eaa-4020-ac53-6e04c964aa0a)
2025-06-18T11:15:37.788+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:15:37.788+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:15:37.788+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:15:37.788+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] be76fec5-7eaa-4020-ac53-6e04c964aa0a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:15:37.788+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:15:37.788+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:15:37.788+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:15:37.789+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:15:37.891+0800	INFO	rpc/rpc_client.go:337	be76fec5-7eaa-4020-ac53-6e04c964aa0a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750216537789_192.168.215.1_28942
2025-06-18T11:15:37.891+0800	INFO	rpc/rpc_client.go:486	be76fec5-7eaa-4020-ac53-6e04c964aa0a notify connected event to listeners , connectionId=1750216537789_192.168.215.1_28942
2025-06-18T11:16:37.977+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:16:38.076+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55900
2025-06-18T11:16:38.076+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=e274a979-432b-4e22-aa36-14bcd7231898)
2025-06-18T11:16:38.076+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:16:38.076+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:16:38.076+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:16:38.076+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] e274a979-432b-4e22-aa36-14bcd7231898 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:16:38.076+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:16:38.076+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:16:38.076+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:16:38.077+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:16:38.179+0800	INFO	rpc/rpc_client.go:337	e274a979-432b-4e22-aa36-14bcd7231898 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750216598077_192.168.215.1_18789
2025-06-18T11:16:38.179+0800	INFO	rpc/rpc_client.go:486	e274a979-432b-4e22-aa36-14bcd7231898 notify connected event to listeners , connectionId=1750216598077_192.168.215.1_18789
2025-06-18T11:27:10.658+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:27:10.757+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55018
2025-06-18T11:27:10.757+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=3ab4cc76-7257-46fb-b449-2d5c2ea14f30)
2025-06-18T11:27:10.757+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:27:10.757+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:27:10.757+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:27:10.757+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 3ab4cc76-7257-46fb-b449-2d5c2ea14f30 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:27:10.757+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:27:10.757+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:27:10.757+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:27:10.758+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:27:10.861+0800	INFO	rpc/rpc_client.go:337	3ab4cc76-7257-46fb-b449-2d5c2ea14f30 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750217230759_192.168.215.1_41248
2025-06-18T11:27:10.861+0800	INFO	rpc/rpc_client.go:486	3ab4cc76-7257-46fb-b449-2d5c2ea14f30 notify connected event to listeners , connectionId=1750217230759_192.168.215.1_41248
2025-06-18T11:32:51.537+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:32:51.613+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55600
2025-06-18T11:32:51.613+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=69510879-bfee-48bc-ab4e-6fba6608a320)
2025-06-18T11:32:51.613+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:32:51.613+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:32:51.613+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:32:51.613+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 69510879-bfee-48bc-ab4e-6fba6608a320 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:32:51.613+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:32:51.613+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:32:51.613+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:32:51.613+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:32:51.716+0800	INFO	rpc/rpc_client.go:337	69510879-bfee-48bc-ab4e-6fba6608a320 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750217571615_192.168.215.1_17174
2025-06-18T11:32:51.716+0800	INFO	rpc/rpc_client.go:486	69510879-bfee-48bc-ab4e-6fba6608a320 notify connected event to listeners , connectionId=1750217571615_192.168.215.1_17174
2025-06-18T11:47:05.781+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:47:05.885+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55377
2025-06-18T11:47:05.885+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9147c2af-98a9-4673-9bfd-e8a907130055)
2025-06-18T11:47:05.885+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:47:05.885+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:47:05.885+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:47:05.885+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9147c2af-98a9-4673-9bfd-e8a907130055 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:47:05.885+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:47:05.885+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:47:05.885+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:47:05.886+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:47:05.989+0800	INFO	rpc/rpc_client.go:337	9147c2af-98a9-4673-9bfd-e8a907130055 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750218425887_192.168.215.1_43368
2025-06-18T11:47:05.989+0800	INFO	rpc/rpc_client.go:486	9147c2af-98a9-4673-9bfd-e8a907130055 notify connected event to listeners , connectionId=1750218425887_192.168.215.1_43368
2025-06-18T11:48:24.067+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T11:48:24.167+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55719
2025-06-18T11:48:24.168+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d753c1dc-b533-4095-9218-04d8265f1cdd)
2025-06-18T11:48:24.168+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T11:48:24.168+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T11:48:24.168+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T11:48:24.168+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d753c1dc-b533-4095-9218-04d8265f1cdd try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T11:48:24.168+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T11:48:24.168+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T11:48:24.168+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T11:48:24.168+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T11:48:24.270+0800	INFO	rpc/rpc_client.go:337	d753c1dc-b533-4095-9218-04d8265f1cdd success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750218504169_192.168.215.1_52983
2025-06-18T11:48:24.270+0800	INFO	rpc/rpc_client.go:486	d753c1dc-b533-4095-9218-04d8265f1cdd notify connected event to listeners , connectionId=1750218504169_192.168.215.1_52983
2025-06-18T12:07:20.768+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T12:07:20.862+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55247
2025-06-18T12:07:20.862+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=bc6a6777-250a-43ad-aa82-c674cf822a2d)
2025-06-18T12:07:20.862+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T12:07:20.862+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T12:07:20.862+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T12:07:20.862+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] bc6a6777-250a-43ad-aa82-c674cf822a2d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T12:07:20.863+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T12:07:20.863+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T12:07:20.863+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T12:07:20.863+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T12:07:20.965+0800	INFO	rpc/rpc_client.go:337	bc6a6777-250a-43ad-aa82-c674cf822a2d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750219640850_192.168.215.1_62148
2025-06-18T12:07:20.965+0800	INFO	rpc/rpc_client.go:486	bc6a6777-250a-43ad-aa82-c674cf822a2d notify connected event to listeners , connectionId=1750219640850_192.168.215.1_62148
2025-06-18T12:08:47.250+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T12:08:47.349+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54961
2025-06-18T12:08:47.353+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a18aaeef-b9b3-4a11-8acd-8a1e561f3aa0)
2025-06-18T12:08:47.353+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T12:08:47.354+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T12:08:47.354+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T12:08:47.354+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a18aaeef-b9b3-4a11-8acd-8a1e561f3aa0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T12:08:47.354+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T12:08:47.354+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T12:08:47.354+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T12:08:47.356+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T12:08:47.459+0800	INFO	rpc/rpc_client.go:337	a18aaeef-b9b3-4a11-8acd-8a1e561f3aa0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750219727346_192.168.215.1_39607
2025-06-18T12:08:47.459+0800	INFO	rpc/rpc_client.go:486	a18aaeef-b9b3-4a11-8acd-8a1e561f3aa0 notify connected event to listeners , connectionId=1750219727346_192.168.215.1_39607
2025-06-18T13:56:46.476+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T13:56:46.560+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54992
2025-06-18T13:56:46.560+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=efffc1bb-66c0-4061-b8d7-b3dd9883fd8e)
2025-06-18T13:56:46.560+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T13:56:46.560+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T13:56:46.561+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T13:56:46.561+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] efffc1bb-66c0-4061-b8d7-b3dd9883fd8e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T13:56:46.561+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T13:56:46.561+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T13:56:46.561+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T13:56:46.561+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T13:56:46.664+0800	INFO	rpc/rpc_client.go:337	efffc1bb-66c0-4061-b8d7-b3dd9883fd8e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226206562_192.168.215.1_25301
2025-06-18T13:56:46.664+0800	INFO	rpc/rpc_client.go:486	efffc1bb-66c0-4061-b8d7-b3dd9883fd8e notify connected event to listeners , connectionId=1750226206562_192.168.215.1_25301
2025-06-18T14:01:28.212+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T14:01:28.311+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55085
2025-06-18T14:01:28.312+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=5ff22000-5298-4609-b66c-86523d3edf3a)
2025-06-18T14:01:28.312+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:01:28.312+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:01:28.312+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:01:28.312+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 5ff22000-5298-4609-b66c-86523d3edf3a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:01:28.312+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:01:28.312+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:01:28.312+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:01:28.312+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T14:01:28.415+0800	INFO	rpc/rpc_client.go:337	5ff22000-5298-4609-b66c-86523d3edf3a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226488313_192.168.215.1_45137
2025-06-18T14:01:28.415+0800	INFO	rpc/rpc_client.go:486	5ff22000-5298-4609-b66c-86523d3edf3a notify connected event to listeners , connectionId=1750226488313_192.168.215.1_45137
2025-06-18T14:05:25.259+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T14:05:25.342+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55544
2025-06-18T14:05:25.342+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=870ed410-9b06-470a-8e1e-1692dfc3db7c)
2025-06-18T14:05:25.342+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:05:25.342+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:05:25.342+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:05:25.342+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 870ed410-9b06-470a-8e1e-1692dfc3db7c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:05:25.342+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:05:25.342+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:05:25.342+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:05:25.343+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T14:05:25.445+0800	INFO	rpc/rpc_client.go:337	870ed410-9b06-470a-8e1e-1692dfc3db7c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750226725344_192.168.215.1_37780
2025-06-18T14:05:25.446+0800	INFO	rpc/rpc_client.go:486	870ed410-9b06-470a-8e1e-1692dfc3db7c notify connected event to listeners , connectionId=1750226725344_192.168.215.1_37780
2025-06-18T14:15:14.349+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T14:15:14.448+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55583
2025-06-18T14:15:14.448+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ce209e3c-2695-4adc-93da-783442cec882)
2025-06-18T14:15:14.448+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:15:14.448+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:15:14.448+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:15:14.448+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ce209e3c-2695-4adc-93da-783442cec882 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:15:14.448+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:15:14.448+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:15:14.449+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:15:14.449+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T14:15:14.552+0800	INFO	rpc/rpc_client.go:337	ce209e3c-2695-4adc-93da-783442cec882 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750227314450_192.168.215.1_29805
2025-06-18T14:15:14.552+0800	INFO	rpc/rpc_client.go:486	ce209e3c-2695-4adc-93da-783442cec882 notify connected event to listeners , connectionId=1750227314450_192.168.215.1_29805
2025-06-18T14:22:18.731+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T14:22:18.830+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55854
2025-06-18T14:22:18.831+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=be932ee5-f294-4b65-a7e2-169a3d9daef4)
2025-06-18T14:22:18.831+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:22:18.831+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:22:18.831+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:22:18.831+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] be932ee5-f294-4b65-a7e2-169a3d9daef4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:22:18.831+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:22:18.831+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:22:18.831+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:22:18.832+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T14:22:18.934+0800	INFO	rpc/rpc_client.go:337	be932ee5-f294-4b65-a7e2-169a3d9daef4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750227738833_192.168.215.1_35266
2025-06-18T14:22:18.935+0800	INFO	rpc/rpc_client.go:486	be932ee5-f294-4b65-a7e2-169a3d9daef4 notify connected event to listeners , connectionId=1750227738833_192.168.215.1_35266
2025-06-18T14:35:06.212+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-18T14:35:06.316+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55252
2025-06-18T14:35:06.316+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=50063368-5509-4d61-b697-ad9ff08d59b3)
2025-06-18T14:35:06.316+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-18T14:35:06.316+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-18T14:35:06.316+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-18T14:35:06.316+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 50063368-5509-4d61-b697-ad9ff08d59b3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-18T14:35:06.316+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-18T14:35:06.316+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-18T14:35:06.316+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-18T14:35:06.319+0800	INFO	util/common.go:96	Local IP:*************
2025-06-18T14:35:06.422+0800	INFO	rpc/rpc_client.go:337	50063368-5509-4d61-b697-ad9ff08d59b3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1750228506320_192.168.215.1_16622
2025-06-18T14:35:06.422+0800	INFO	rpc/rpc_client.go:486	50063368-5509-4d61-b697-ad9ff08d59b3 notify connected event to listeners , connectionId=1750228506320_192.168.215.1_16622
2025-07-03T14:11:19.356+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-03T14:11:19.357+0800	ERROR	nacos_server/nacos_server.go:98	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-07-03T14:11:19.358+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54954
2025-07-03T14:11:19.358+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2a423f74-571c-49c2-9635-eae313a053c0)
2025-07-03T14:11:19.358+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-03T14:11:19.358+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-03T14:11:19.358+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-03T14:11:19.358+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.358+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.358+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.358+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.359+0800	INFO	util/common.go:96	Local IP:************
2025-07-03T14:11:19.359+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-07-03T14:11:19.359+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.359+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-07-03T14:11:19.359+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-03T14:11:19.359+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 2a423f74-571c-49c2-9635-eae313a053c0 fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-07-03T14:11:19.359+0800	INFO	rpc/rpc_client.go:426	2a423f74-571c-49c2-9635-eae313a053c0 try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-03T14:11:19.359+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T16:58:46.523+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-08T16:58:46.637+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55861
2025-07-08T16:58:46.637+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ffb0888b-3fba-4f1e-a7ff-cf8717e363f2)
2025-07-08T16:58:46.637+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T16:58:46.637+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T16:58:46.637+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T16:58:46.637+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ffb0888b-3fba-4f1e-a7ff-cf8717e363f2 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T16:58:46.637+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T16:58:46.637+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T16:58:46.637+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T16:58:46.638+0800	INFO	util/common.go:96	Local IP:************
2025-07-08T16:58:46.744+0800	INFO	rpc/rpc_client.go:337	ffb0888b-3fba-4f1e-a7ff-cf8717e363f2 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751965126641_192.168.215.1_32424
2025-07-08T16:58:46.745+0800	INFO	rpc/rpc_client.go:486	ffb0888b-3fba-4f1e-a7ff-cf8717e363f2 notify connected event to listeners , connectionId=1751965126641_192.168.215.1_32424
2025-07-08T17:04:36.053+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-08T17:04:36.157+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55064
2025-07-08T17:04:36.158+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=14d33b1e-d0b1-4f10-9ae9-1ce7bfe5b9c7)
2025-07-08T17:04:36.158+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:04:36.158+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:04:36.158+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:04:36.159+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 14d33b1e-d0b1-4f10-9ae9-1ce7bfe5b9c7 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:04:36.159+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:04:36.159+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:04:36.159+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:04:36.159+0800	INFO	util/common.go:96	Local IP:************
2025-07-08T17:04:36.264+0800	INFO	rpc/rpc_client.go:337	14d33b1e-d0b1-4f10-9ae9-1ce7bfe5b9c7 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751965476150_192.168.215.1_47610
2025-07-08T17:20:11.153+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-08T17:20:11.261+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55467
2025-07-08T17:20:11.261+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=69f813a7-71f1-4023-b9f8-154ce05d46a4)
2025-07-08T17:20:11.261+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:20:11.261+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:20:11.261+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:20:11.261+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 69f813a7-71f1-4023-b9f8-154ce05d46a4 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:20:11.261+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:20:11.261+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:20:11.261+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:20:11.262+0800	INFO	util/common.go:96	Local IP:************
2025-07-08T17:20:11.366+0800	INFO	rpc/rpc_client.go:337	69f813a7-71f1-4023-b9f8-154ce05d46a4 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751966411263_192.168.215.1_35813
2025-07-08T17:20:11.366+0800	INFO	rpc/rpc_client.go:486	69f813a7-71f1-4023-b9f8-154ce05d46a4 notify connected event to listeners , connectionId=1751966411263_192.168.215.1_35813
2025-07-08T17:37:21.905+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-08T17:37:22.006+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55693
2025-07-08T17:37:22.006+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=542db9d2-13d0-44a8-a05f-8213dc8adca3)
2025-07-08T17:37:22.006+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-08T17:37:22.006+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-08T17:37:22.006+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-08T17:37:22.006+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 542db9d2-13d0-44a8-a05f-8213dc8adca3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-08T17:37:22.006+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-08T17:37:22.006+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-08T17:37:22.006+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-08T17:37:22.007+0800	INFO	util/common.go:96	Local IP:************
2025-07-08T17:37:22.111+0800	INFO	rpc/rpc_client.go:337	542db9d2-13d0-44a8-a05f-8213dc8adca3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1751967442008_192.168.215.1_51515
2025-07-10T19:26:18.916+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-07-10T19:26:18.916+0800	ERROR	nacos_server/nacos_server.go:98	login in err:Post "http://127.0.0.1:8848/nacos/v1/auth/users/login": dial tcp 127.0.0.1:8848: connect: connection refused
2025-07-10T19:26:18.917+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55822
2025-07-10T19:26:18.917+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=838c55ff-e95c-4efa-aa85-cf3d611d7ebf)
2025-07-10T19:26:18.917+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-07-10T19:26:18.917+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-07-10T19:26:18.917+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-07-10T19:26:18.917+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.917+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.917+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.917+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.917+0800	INFO	util/common.go:96	Local IP:**********
2025-07-10T19:26:18.918+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=2
2025-07-10T19:26:18.918+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.918+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=1
2025-07-10T19:26:18.918+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-07-10T19:26:18.918+0800	WARN	rpc/rpc_client.go:329	[RpcClient.Start] 838c55ff-e95c-4efa-aa85-cf3d611d7ebf fail to connect to server on start up, error message=server check request failed , err:rpc error: code = Unavailable desc = connection error: desc = "transport: Error while dialing: dial tcp 127.0.0.1:9848: connect: connection refused", start up retry times left=0
2025-07-10T19:26:18.918+0800	INFO	rpc/rpc_client.go:426	838c55ff-e95c-4efa-aa85-cf3d611d7ebf try to re connect to a new server, server is not appointed, will choose a random server.
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-07-10T19:26:18.918+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
