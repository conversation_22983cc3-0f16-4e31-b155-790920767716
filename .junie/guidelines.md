# 準則
## 通用規則

- 請使用中文回覆我
- 請用審視的目光，來看待我的輸入。 如果有問題請指出，並跳出我的思考框架給出我建議
- 如果我的方向有錯，請明確指出，讓我瞬間清醒
## 角色定義
- 當你在幫我撰寫和分析代碼的時候，你是一個資深的全棧開發工程師。你和goframe 作者一起維護過代碼精通goframe ，熟悉使用goframe 的最新版本。
- 當你在幫我撰寫文件的時候， 你是一個資深的軟件產品經理，並且也是20年以上開發經驗的全棧工程師（精通goframe框架的使用和開發）

## 撰寫文檔要求
- 要先理解整個項目的源代碼。並進行深度思考
- 在審視所有代碼之後開始撰寫文檔
- 輸出採用繁體中文
- 可以用圖表進行表達的部分都會用圖表進行表達

# DataSyncHub 开发者指南

## 项目概述
DataSyncHub 是一个基于 GoFrame 构建的数据同步服务，用于连接和同步不同存储系统之间的数据，包括：
- MariaDB/MySQL 关系型数据库
- Weaviate 向量数据库
- RabbitMQ 消息队列

该应用程序设计为可通过 Nacos 注册和发现的微服务。

## 项目结构

```
dataSyncHub/
├── api/                  # API 定义和请求/响应结构
│   ├── executeapi/       # SQL 执行 API
│   ├── queryapi/         # 数据查询 API
│   └── vector/           # 向量数据库 API
├── boot/                 # 应用程序初始化
├── docs/                 # 文档
├── hack/                 # 开发脚本和工具
├── internal/             # 内部应用程序代码
│   ├── cmd/              # 命令行命令
│   ├── consts/           # 常量
│   ├── controller/       # HTTP 控制器
│   ├── dao/              # 数据访问对象
│   ├── logic/            # 业务逻辑
│   ├── model/            # 数据模型
│   ├── packed/           # 打包资源
│   └── service/          # 服务接口
├── logs/                 # 日志文件
├── manifest/             # 部署清单
│   ├── config/           # 配置文件
│   ├── deploy/           # Kubernetes 部署文件
│   └── docker/           # Docker 相关文件
├── nacos/                # Nacos 配置
└── main.go               # 应用程序入口点
```

## 技术栈
- **框架**: GoFrame (gf) v2
- **数据库**:
  - MariaDB/MySQL 关系型数据
  - Weaviate 向量数据库
- **消息队列**: RabbitMQ
- **服务注册与配置**: Nacos
- **容器化**: Docker
- **编排**: Kubernetes

## 安装与配置

### 前提条件
- Go 1.18 或更高版本
- Docker 和 Docker Compose
- 访问 MariaDB、Weaviate、RabbitMQ 和 Nacos 实例的权限

### 配置
1. 配置通过 Nacos 管理。应用程序在启动时将连接到 Nacos 获取其配置。
2. 本地开发可以使用 `manifest/config/` 中的配置文件。

### 构建应用程序
```bash
# 构建应用程序
make build

# 构建 Docker 镜像
make docker
```

## 运行应用程序

### 本地开发
```bash
# 本地运行应用程序
go run main.go

# 或使用运行脚本
./run.sh
```

### 使用 Docker
```bash
# 使用 Docker 运行
docker run -p 8000:8000 dataSyncHub
```

## 测试

### 运行测试
```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./internal/logic/mariadb/...
go test ./internal/logic/vector/...
go test ./internal/logic/messageQ/...
```

### 测试文件
- 测试文件与实现文件位于同一目录，后缀为 `_test.go`
- 示例测试文件:
  - `internal/logic/mariadb/mariadb_test.go`
  - `internal/logic/vector/vec_weaviate_test.go`
  - `internal/logic/messageQ/messageQ_test.go`

## API 文档

### 主要 API 端点
- **执行 API**: 用于执行 SQL 语句
  - `POST /executeapi/v1/execute-sql`
  - `POST /executeapi/v1/batch-execute-sql`

- **查询 API**: 用于查询数据
  - `POST /queryapi/v1/data-query`

- **向量 API**: 用于向量数据库操作
  - `POST /vector/v1/create-collection`
  - `POST /vector/v1/create-data`
  - `POST /vector/v1/similarity-search`
  - `POST /vector/v1/hybrid-search`
  - `POST /vector/v1/fetch-records`
  - `POST /vector/v1/get-all-records`

## 开发指南

### 代码组织
1. **API 定义**: 在 `api/` 下的适当目录中放置新的 API 定义
2. **控制器**: 在 `internal/controller/` 中实现 API 处理程序
3. **业务逻辑**: 在 `internal/logic/` 中实现核心逻辑
4. **服务接口**: 在 `internal/service/` 中定义服务接口

### 添加新功能
1. 在适当的 API 包中定义 API
2. 创建或更新服务接口
3. 实现业务逻辑
4. 创建控制器来处理 API 请求
5. 在 `internal/cmd/cmd.go` 中注册新路由

### 错误处理
- 使用 GoFrame 的 `gerror` 进行错误处理
- 在 `internal/consts/errors.go` 中定义常见错误
- 使用基于类别的日志系统适当记录错误

### 日志记录
- 使用 GoFrame 的日志系统，包含以下类别:
  - `CatHttpLogs` 用于 HTTP 请求/响应
  - `CatDB` 用于数据库操作
  - `CatMQ` 用于消息队列操作
  - `CatWeaviate` 用于向量数据库操作

### 消息队列
- 在 `cmd.Main.Func` 中注册消息处理程序
- 为不同类型的消息使用适当的路由键前缀

## 部署

### Kubernetes
Kubernetes 清单文件位于 `manifest/deploy/kustomize/`:
```bash
# 应用基本配置
kubectl apply -k manifest/deploy/kustomize/base

# 应用环境特定配置
kubectl apply -k manifest/deploy/kustomize/overlays/develop
```

### Docker
```bash
# 构建并推送 Docker 镜像
make docker
docker push <registry>/dataSyncHub:<tag>
```

## 最佳实践

### 代码风格
- 遵循 Go 标准代码风格和约定
- 使用 GoFrame 的控制器和服务实现约定
- 使用有意义的变量和函数名

### 测试
- 为所有业务逻辑编写单元测试
- 使用 GoFrame 的测试工具
- 测试时模拟外部依赖

### 配置
- 使用 Nacos 进行配置管理
- 不要硬编码配置值
- 使用环境变量存储敏感信息

### 性能
- 使用数据库连接池
- 实现适当的错误处理和重试机制
- 使用 context 进行取消和超时控制

### 安全
- 验证所有用户输入
- 使用预处理语句进行数据库查询
- 实现适当的身份验证和授权

## 故障排除

### 常见问题
- **连接问题**: 检查与 MariaDB、Weaviate、RabbitMQ 和 Nacos 的网络连接
- **配置问题**: 验证 Nacos 配置是否正确
- **性能问题**: 检查数据库查询性能和连接池设置

### 日志
- 检查 `logs/` 目录中的应用程序日志
- 不同组件的日志记录在不同文件中:
  - HTTP 日志: `logs/http-logs/`
  - 数据库日志: `logs/db/`
  - 消息队列日志: `logs/MQ/`
  - Weaviate 日志: `logs/weaviate/`

## 贡献
1. 从 `develop` 创建功能分支
2. 进行更改
3. 编写或更新测试
4. 提交拉取请求
5. 确保 CI/CD 管道通过
