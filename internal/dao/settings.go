// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"dataSyncHub/internal/dao/internal"
)

// settingsDao is the data access object for the table settings.
// You can define custom methods on it to extend its functionality as needed.
type settingsDao struct {
	*internal.SettingsDao
}

var (
	// Settings is a globally accessible object for table settings operations.
	Settings = settingsDao{internal.NewSettingsDao()}
)

// Add your custom methods and functionality below.
