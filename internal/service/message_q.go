// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"dataSyncHub/internal/consts"
)

type (
	IMessageQ interface {
		RegisterHandler(routeKeyPrefix string, handler consts.OnMessage)
		InitReceive(ctx context.Context) (err error)
		IsHealthy() bool
		Close() error
		GetConnectionStatus() map[string]interface{}
	}
)

var (
	localMessageQ IMessageQ
)

func MessageQ() IMessageQ {
	if localMessageQ == nil {
		panic("implement not found for interface IMessageQ, forgot register?")
	}
	return localMessageQ
}

func RegisterMessageQ(i IMessageQ) {
	localMessageQ = i
}
