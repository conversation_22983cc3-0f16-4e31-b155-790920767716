// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"dataSyncHub/internal/model"
)

type (
	ISettings interface {
		InitializeDatabase(ctx context.Context) error
		Get(ctx context.Context, in model.GetSettingsInput) (value string, err error)
		Set(ctx context.Context, in model.SetSettingsInput) (err error)
	}
)

var (
	localSettings ISettings
)

func Settings() ISettings {
	if localSettings == nil {
		panic("implement not found for interface ISettings, forgot register?")
	}
	return localSettings
}

func RegisterSettings(i ISettings) {
	localSettings = i
}
