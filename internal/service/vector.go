// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"dataSyncHub/internal/model"

	"github.com/gogf/gf/v2/container/gmap"
)

type (
	IVecWeaviate interface {
		OnMessage(ctx context.Context, message any)
		UpdateVector(ctx context.Context, in *model.UpdateVectorInput) (err error)
		CreateCollection(ctx context.Context, in model.CreateCollectionInput) (err error)
		AddNewProperties(ctx context.Context, in *model.AddNewPropertiesInput) (err error)
		ClearDataByFilter(ctx context.Context, in *model.ClearDataByFilterInput) (err error)
		EmptyCollection(ctx context.Context, in *model.EmptyCollectionInput) (err error)
		DeleteCollection(ctx context.Context, collections []string) (err error)
		DeleteTenants(ctx context.Context, in *model.DeleteTenantsInput) (err error)
		CreateSingleData(ctx context.Context, in model.CollectionData) (id string, err error)
		CreateData(ctx context.Context, in *model.CreateDataInput) (out *model.CreateDataOutput, err error)
		FetchRecords(ctx context.Context, in model.FetchRecordsInput) (out *model.FetchRecordsOutput, err error)
		GetAllRecords(ctx context.Context, in model.GetAllRecordsInput) (out *model.GetAllRecordsOutput, err error)
		GetPropertyNames(ctx context.Context, collection string) []string
		GetProperties(ctx context.Context, in model.GetPropertiesInput) (out *model.GetPropertiesOutput, err error)
		GetPropertiesByGroup(ctx context.Context, in model.GetPropertiesByGroupInput) (out *model.GetPropertiesByGroupOutput, err error)
		UpdateProperties(ctx context.Context, in *model.UpdatePropertiesInput) (err error)
		// CreateTenantIfNotExist ensures that a tenant exists for specified collections. Creates tenant if it does not exist.
		// It validates the input, checks for tenant existence, and conditionally creates the tenant using the provided client.
		// Returns an error if validation, existence check, or tenant creation fails.
		CreateTenantIfNotExist(ctx context.Context, in *model.CreateTenantIfNotExistInput) (err error)
		SimilaritySearch(ctx context.Context, in model.SimilaritySearchInput) (out *model.SimilaritySearchOutput, err error)
		HybridSearch(ctx context.Context, in model.HybridSearchInput) (out *model.HybridSearchOutput, err error)
		GetTenantAndCollections(ctx context.Context, collections []string) (out *gmap.StrAnyMap, err error)
	}
)

var (
	localVecWeaviate IVecWeaviate
)

func VecWeaviate() IVecWeaviate {
	if localVecWeaviate == nil {
		panic("implement not found for interface IVecWeaviate, forgot register?")
	}
	return localVecWeaviate
}

func RegisterVecWeaviate(i IVecWeaviate) {
	localVecWeaviate = i
}
