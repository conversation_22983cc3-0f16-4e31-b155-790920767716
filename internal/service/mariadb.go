// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"dataSyncHub/internal/model"

	"golang.org/x/net/context"
)

type (
	IMariaDB interface {
		ExecuteSQL(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error)
		BatchExecuteSQL(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error)
		OnMessage(ctx context.Context, message any)
		GetContents(ctx context.Context, in model.GetContentsReq) (out *model.GetContentsRes, err error)
	}
)

var (
	localMariaDB IMariaDB
)

func MariaDB() IMariaDB {
	if localMariaDB == nil {
		panic("implement not found for interface IMariaDB, forgot register?")
	}
	return localMariaDB
}

func RegisterMariaDB(i IMariaDB) {
	localMariaDB = i
}
