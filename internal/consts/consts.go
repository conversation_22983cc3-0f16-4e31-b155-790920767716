package consts

import "context"

var ServiceName = "dsh.svc"

const EmptyFilter = "where:{}"
const (
	CatWeaviate = "weaviate"
	CatMQ       = "MQ"
	CatDB       = "DB"
	CatHttpLogs = "http-logs"
)
const (
	SettingsVector = "vector"
	SettingsTenant = "tenant"
)

// provider
const (
	ProviderAzure            = "azure"
	ProviderGoogleVertex     = "google.vertex"
	ProviderGoogleStudio     = "google.studio"
	HeaderAzureAPIKey        = "X-Azure-Api-Key"
	HeaderGoogleStudioAPIKey = "X-Goog-Studio-Key"
)
const XHeaderService = "X-SERVICE"

// Redis 緩存相關常量
const (
	// 緩存 key 前綴
	CacheKeyPrefix = "DSH"

	// Schema 列表緩存 key（使用 Redis Set 存儲所有已創建的 schema）
	CacheKeySchemas = "DSH:schemas"

	// Table 列表緩存 key 模板（使用 Redis Set 存儲指定 schema 下的 table 列表）
	CacheKeyTables = "DSH:tables:%s"

	// 分佈式鎖 key 模板
	LockKeySchema = "DSH:lock:schema:%s"
	LockKeyTable  = "DSH:lock:table:%s:%s"
)

// 緩存過期時間常量
const (
	// Schema 緩存過期時間（24小時）
	CacheExpireSchema = 24 * 60 * 60 // 24小時，單位：秒

	// Table 緩存過期時間（12小時）
	CacheExpireTable = 12 * 60 * 60 // 12小時，單位：秒

	// 分佈式鎖過期時間（30秒）
	LockExpireTime = 30 // 30秒
)

// 字段比對鍵名常量
const (
	CompareKeyFileName    = "file_name"
	CompareKeyWebsiteURL  = "website_url"
	CompareKeyYoutubeLink = "youtube_link"
	CompareKeyPlainText   = "plain_text"
)

// type of function
type OnMessage func(ctx context.Context, message any)
