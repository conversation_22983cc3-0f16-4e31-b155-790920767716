package consts

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/weaviate/weaviate/entities/models"
	"github.com/weaviate/weaviate/entities/schema"
)

// modelConfig 用于配置 Weaviate 中的模块参数
var moduleConfig = map[string]interface{}{
	"text2vec-openai": map[string]interface{}{
		"skip":                  true,
		"vectorizePropertyName": false,
	},
}

const ChatMessage = `chat_message`
const (
	PropMessage     = "message"
	PropServiceID   = "service_id"
	PropUserID      = "user_id"
	PropRole        = "role"
	PropChannel     = "channel"
	PropMessageType = "message_type"
	PropDisplayName = "display_name"
	PropCrateAt     = "crate_at"
)

// CollectionPros 用于定义 Weaviate 中的集合属性
var CollectionPros = g.Map{
	ChatMessage: []*models.Property{
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         PropMessage,
			ModuleConfig: moduleConfig,
			Tokenization: models.PropertyTokenizationGse,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         PropServiceID,
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         PropUserID,
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         PropRole,
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         PropChannel,
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         ",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeText.String()},
			Name:         "display_name",
			ModuleConfig: moduleConfig,
		},
		{
			DataType:     []string{schema.DataTypeDate.String()},
			Name:         "crate_at",
			ModuleConfig: moduleConfig,
		},
	},
}
