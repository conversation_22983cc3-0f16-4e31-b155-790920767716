// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Settings is the golang structure for table settings.
type Settings struct {
	SettingName string `json:"setting_name" orm:"setting_name" ` //
	Settings    string `json:"settings"     orm:"settings"     ` //
}
