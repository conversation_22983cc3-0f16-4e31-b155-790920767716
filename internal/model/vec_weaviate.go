package model

import (
	"dataSyncHub/internal/consts"
	"dataSyncHub/utility"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate/entities/models"
)

type UpdateVectorInput struct {
	Tenant     string    `json:"tenant" v:"required"`
	Collection string    `json:"collection" v:"required"`
	ID         string    `json:"id" v:"required"`
	Vector     []float32 `json:"-" v:"required"`
}

func (i *UpdateVectorInput) String() string {
	js := gjson.New(i)
	strIn := js.MustToJsonIndentString()
	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}
	return strIn
}

type CreateCollectionInput struct {
	VectorCollectionSetting
	Tenants       []string `json:"tenants"`
	RenewSettings bool     `json:"renew_settings"`
}

type AddNewPropertiesInput struct {
	CollectionName string             `json:"collection_name" v:"required"`
	Properties     []*models.Property `json:"properties"`
}
type EmptyCollectionInput struct {
	CollectionName string `json:"collection_name" v:"required"`
	Tenant         string `json:"tenant"`
}
type ClearDataByFilterInput struct {
	CollectionName string `json:"collection_name" v:"required"`
	Tenant         string `json:"tenant"`
	Filter         string `json:"filter"`
}

func (i *ClearDataByFilterInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {

	if g.IsEmpty(i.Filter) || i.Filter == consts.EmptyFilter {
		return nil, gerror.New("filter is empty")
	}

	where, err = utility.BuildWhereFromJSON(i.Filter)

	return
}

type DeleteTenantsInput struct {
	Collections []string `json:"collections" v:"required"`
	Tenants     []string `json:"tenants" v:"required"`
}
type CollectionData struct {
	ID         string    `json:"id"`
	Tenant     string    `json:"tenant"`
	Collection string    `json:"collection"`
	Vector     []float32 `json:"vector"`
	Properties g.Map     `json:"properties"`
}

func (i *CollectionData) String() string {
	js := gjson.New(i)
	strIn := js.MustToJsonIndentString()
	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}
	return strIn
}

type CreateDataInput struct {
	Data []*CollectionData `json:"data"`
}

func (i *CreateDataInput) String() string {
	var jsData = gjson.New(i)
	strIn := jsData.MustToJsonString()

	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}

	return strIn
}

type CreateDataOutput struct {
	IDs     []string `json:"ids"`
	Total   int      `json:"total"`
	Success int      `json:"success"`
	Fail    int      `json:"fail"`
}
type FetchRecordsInput struct {
	Tenant             string   `json:"tenant" `
	Collection         string   `json:"collection" v:"required"`
	Properties         []string `json:"properties" v:"required"`
	PageSize           int      `json:"page_size" v:"required"`
	Offset             int      `json:"offset"`
	Filter             string   `json:"filter"`
	OriginalAdditional bool     `json:"original_additional"`
}

func (i *FetchRecordsInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {
	if g.IsEmpty(i.Filter) || i.Filter == consts.EmptyFilter {
		return nil, nil
	}

	where, err = utility.BuildWhereFromJSON(i.Filter)

	return
}

type FetchRecordsOutput struct {
	Records []g.Map `json:"records"`
}

func (i *FetchRecordsInput) String() string {
	js := gjson.New(i)
	return js.MustToJsonIndentString()
}

type GetAllRecordsInput struct {
	Tenant     string   `json:"tenant" `
	Collection string   `json:"collection" v:"required"`
	Properties []string `json:"properties" v:"required"`
	PageSize   int      `json:"page_size"`
	Filter     string   `json:"filter"`
}

func (f *GetAllRecordsInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {
	if g.IsEmpty(f.Filter) || f.Filter == consts.EmptyFilter {
		return nil, nil
	}

	where, err = utility.BuildWhereFromJSON(f.Filter)

	return
}

func (f *GetAllRecordsInput) String() string {
	js := gjson.New(f)
	return js.MustToJsonIndentString()
}

type GetAllRecordsOutput struct {
	Records []map[string]any `json:"records"`
}
type GetPropertiesInput struct {
	Tenant     string   `json:"tenant" `
	Collection string   `json:"collection" v:"required"`
	IDs        []string `json:"ids" v:"required"`
	WithVector bool     `json:"with_vector"`
}

type GetPropertiesOutput struct {
	IDToProperties map[string]map[string]any `json:"id_to_properties"`
}
type GetPropertiesByGroupInput struct {
	Tenant            string   `json:"tenant"`
	Collection        string   `json:"collection" v:"required"`
	Properties        []string `json:"properties" v:"required"`
	Filter            string   `json:"filter" v:"required"`
	GroupedFields     []string `json:"grouped_fields" v:"required"`
	MaxGroups         int      `json:"max_groups" v:"required"`
	MaxObjectPerGroup int      `json:"max_object_per_group" v:"required"`
	KeyProp           string   `json:"key_prop" `
	ValueProp         string   `json:"value_prop"`
}

func (i *GetPropertiesByGroupInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {
	if g.IsEmpty(i.Filter) || i.Filter == consts.EmptyFilter {
		return nil, nil
	}

	where, err = utility.BuildWhereFromJSON(i.Filter)

	return
}

type GetPropertiesByGroupOutput struct {
	OutputMap  map[string]string `json:"output_map"`
	OutputData []map[string]any  `json:"output_data"`
}
type UpdatePropertiesInput struct {
	Tenant     string    `json:"tenant"`
	Collection string    `json:"collection" v:"required"`
	ID         string    `json:"id" v:"required"`
	Properties g.Map     `json:"properties" `
	Vector     []float32 `json:"vector"`
}

func (i *UpdatePropertiesInput) String() string {
	js := gjson.New(i)
	strIn := js.MustToJsonIndentString()
	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}
	return strIn
}

type CreateTenantIfNotExistInput struct {
	Tenant      string   `json:"tenant" v:"required"`
	Collections []string `json:"collections" v:"required"`
}
type SimilaritySearchInput struct {
	Tenant             string    `json:"tenant"`
	Collection         string    `json:"collection" v:"required"`
	Properties         []string  `json:"properties" v:"required"`
	Vector             []float32 `json:"vector" v:"required"`
	Distance           float32   `json:"distance" v:"required"`
	Limit              int       `json:"limit" v:"required"`
	Filter             string    `json:"filter"`
	OriginalAdditional bool      `json:"original_additional"`
}

func (i *SimilaritySearchInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {
	if g.IsEmpty(i.Filter) || i.Filter == consts.EmptyFilter {
		return nil, nil
	}

	where, err = utility.BuildWhereFromJSON(i.Filter)

	return
}

func (i *SimilaritySearchInput) String() string {
	js := gjson.New(i)
	strIn := js.MustToJsonIndentString()
	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}
	return strIn
}

type SimilaritySearchOutput struct {
	Properties []map[string]any `json:"properties"`
}
type HybridSearchInput struct {
	Tenant             string    `json:"tenant" `
	Collection         string    `json:"collection" v:"required"`
	Query              string    `json:"query" v:"required"`
	Properties         []string  `json:"properties" v:"required"`
	Vector             []float32 `json:"vector" v:"required"`
	Filter             string    `json:"filter" v:"required"`
	Alpha              float32   `json:"alpha" v:"required"`
	PropertyWeight     []string  `json:"property_weight" v:"required"`
	FusionType         string    `json:"fusion_type" v:"required"`
	Limit              int       `json:"limit" v:"required"`
	OriginalAdditional bool      `json:"original_additional"`
}

func (i *HybridSearchInput) GetWhereBuilder() (where *filters.WhereBuilder, err error) {
	if g.IsEmpty(i.Filter) || i.Filter == consts.EmptyFilter {
		return nil, nil
	}

	where, err = utility.BuildWhereFromJSON(i.Filter)

	return
}
func (i *HybridSearchInput) String() string {
	js := gjson.New(i)
	strIn := js.MustToJsonIndentString()
	newStr, err := utility.ProcessVectorJSON(strIn)
	if err == nil {
		strIn = newStr
	}
	return strIn
}

type HybridSearchOutput struct {
	Properties []map[string]any `json:"properties"`
}
