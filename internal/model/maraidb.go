package model

import (
	"github.com/gogf/gf/v2/frame/g"
)

type GetContentsReq struct {
	Schema    string  `json:"schema"`
	Table     string  `json:"table"`
	WhereCond string  `json:"where_cond"`
	Params    g.Slice `json:"params"`
	Fields    g.Slice `json:"fields"`
	Limit     int     `json:"limit"`
	RawSQL    string  `json:"raw_sql"`
	Order     string  `json:"order"`
}
type GetContentsRes struct {
	Contents []map[string]any `json:"contents"`
	Code     int              `json:"code"`
	Message  string           `json:"message"`
	Cost     string           `json:"cost"`
}

type ExecuteSQLInput struct {
	Schema string `json:"schema" v:"required"`
	RawSQL string `json:"raw_sql" v:"required"`
}
type ExecuteSQLOutput struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type BatchExecuteSQLInput struct {
	Schema  string   `json:"schema" v:"required"`
	SQLList []string `json:"sql_list" v:"required"`
}
type BatchExecuteSQLOutput struct {
	Success      bool     `json:"success"`
	TotalCount   int      `json:"total_count"`
	SuccessCount int      `json:"success_count"`
	FailCount    int      `json:"fail_count"`
	Errors       []string `json:"errors"`
}
