package model

import "github.com/weaviate/weaviate/entities/models"

type CreateCollectionInput struct {
	VectorCollectionSetting
	Tenants       []string `json:"tenants"`
	RenewSettings bool     `json:"renew_settings"`
}
type VectorCollectionSetting struct {
	CollectionName   string             `json:"collection_name"`
	MultiTenancy     bool               `json:"multi_tenancy"`
	VectorProperties []string           `json:"vector_properties"`
	Properties       []*models.Property `json:"properties"`
}

type ClearDataByFilterInput struct {
	CollectionName string `json:"collection_name" v:"required"`
	Tenant         string `json:"tenant"`
	Filter         string `json:"filter"`
}
type CreateDataInput struct {
	Data []*CollectionData `json:"data"`
}
type CollectionData struct {
	ID         string    `json:"id"`
	Tenant     string    `json:"tenant"`
	Collection string    `json:"collection"`
	Vector     []float32 `json:"vector"`
	Properties g.Map     `json:"properties"`
}
