package messageQ

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"sync"
	"time"
)
import amqp "github.com/rabbitmq/amqp091-go"

func init() {
	service.RegisterMessageQ(New())
}

type sMessageQ struct {
	conn            *amqp.Connection
	channel         *amqp.Channel
	messageHandlers map[string]consts.OnMessage

	// Connection state management
	isConnected  bool
	isConnecting bool
	isClosed     bool
	url          string

	// Control channels
	reconnectChan chan struct{}
	closeChan     chan struct{}

	// Synchronization control
	mu sync.RWMutex

	// Reconnection configuration
	maxRetries           int
	retryInterval        time.Duration
	maxReconnectInterval time.Duration
	reconnectCount       int
	lastReconnectTime    time.Time

	// 消息處理並發控制
	messageWorkerCount int
	messageWorkerSem   chan struct{}
}

func (s *sMessageQ) logger() glog.ILogger {
	return g.Log().Cat(consts.CatMQ)
}

func New() service.IMessageQ {
	return &sMessageQ{
		messageHandlers:      make(map[string]consts.OnMessage),
		reconnectChan:        make(chan struct{}, 1),
		closeChan:            make(chan struct{}),
		maxRetries:           5,
		retryInterval:        time.Second * 5,
		maxReconnectInterval: time.Minute * 5, // 最大重連間隔 5 分鐘
		messageWorkerCount:   10,              // 消息處理並發數
		messageWorkerSem:     make(chan struct{}, 10),
	}
}

func (s *sMessageQ) RegisterHandler(routeKeyPrefix string, handler consts.OnMessage) {
	s.messageHandlers[routeKeyPrefix] = handler
}

// dispatchMessage 分發消息到對應的處理器，支持並發處理和錯誤恢復
func (s *sMessageQ) dispatchMessage(ctx context.Context, msg amqp.Delivery) {
	s.logger().Infof(ctx, "receive message route key: %s", msg.RoutingKey)

	routingKey := msg.RoutingKey
	handlerFound := false

	// 遍歷處理器，找到匹配的前綴
	for prefix, handler := range s.messageHandlers {
		if gstr.HasPrefix(routingKey, prefix) {
			handlerFound = true
			// 使用 goroutine 並發處理消息，避免阻塞
			g.Go(ctx, func(ctx context.Context) {
				s.processMessageWithRecovery(ctx, msg, handler, prefix)
			}, func(ctx context.Context, exception error) {
				s.logger().Errorf(ctx, "Message processing goroutine exception for prefix %s: %v", prefix, gerror.Stack(exception))
			})
			// 找到匹配的處理器後繼續，因為可能有多個匹配的前綴
		}
	}

	if !handlerFound {
		s.logger().Warningf(ctx, "No handler found for routing key: %s", routingKey)
	}
}

// processMessageWithRecovery 處理單個消息，包含錯誤恢復機制
func (s *sMessageQ) processMessageWithRecovery(ctx context.Context, msg amqp.Delivery, handler consts.OnMessage, prefix string) {
	// 獲取信號量，控制並發數
	s.messageWorkerSem <- struct{}{}
	defer func() { <-s.messageWorkerSem }()

	// 添加 panic 恢復機制
	defer func() {
		if r := recover(); r != nil {
			s.logger().Errorf(ctx, "Message handler panic recovered for prefix %s, routing key %s: %v",
				prefix, msg.RoutingKey, r)
		}
	}()

	startTime := time.Now()

	// 執行消息處理
	handler(ctx, msg)

	processingTime := time.Since(startTime)
	s.logger().Debugf(ctx, "Message processed successfully for prefix %s, routing key %s, processing time: %v",
		prefix, msg.RoutingKey, processingTime)
}

func (s *sMessageQ) InitReceive(ctx context.Context) (err error) {
	url, err := g.Cfg().Get(ctx, "rabbitMQ.url")
	if err != nil {
		return gerror.Wrap(err, "Failed to get RabbitMQ configuration")
	}

	s.url = url.String()

	// Initial connection test
	if err = s.testConnection(ctx); err != nil {
		return gerror.Wrap(err, "RabbitMQ initial connection test failed")
	}

	// Start connection maintenance goroutine
	g.Go(ctx, func(ctx context.Context) {
		s.maintainConnection(ctx)
	}, func(ctx context.Context, exception error) {
		s.logger().Error(ctx, "Connection maintenance goroutine exception:", gerror.Stack(exception))
		// Attempt to restart connection maintenance
		s.triggerReconnect()
	})

	// Start message consumer goroutine
	g.Go(ctx, func(ctx context.Context) {
		s.startMessageConsumer(ctx)
	}, func(ctx context.Context, exception error) {
		s.logger().Error(ctx, "Message consumer goroutine exception:", gerror.Stack(exception))
		// Attempt to restart message consumer
		s.triggerReconnect()
	})

	return nil
}

// testConnection tests RabbitMQ connection
func (s *sMessageQ) testConnection(ctx context.Context) error {
	conn, err := amqp.Dial(s.url)
	if err != nil {
		return gerror.Wrap(err, "Unable to connect to RabbitMQ")
	}
	defer conn.Close()

	channel, err := conn.Channel()
	if err != nil {
		return gerror.Wrap(err, "Unable to create RabbitMQ channel")
	}
	defer channel.Close()

	s.logger().Info(ctx, "RabbitMQ connection test successful")
	return nil
}

// connect establishes RabbitMQ connection
func (s *sMessageQ) connect(ctx context.Context) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isConnecting {
		return nil // Connection in progress, avoid duplicate connections
	}

	s.isConnecting = true
	defer func() {
		s.isConnecting = false
	}()

	// Close existing connection
	s.closeConnectionUnsafe()

	// Establish new connection
	conn, err := amqp.Dial(s.url)
	if err != nil {
		s.isConnected = false
		return gerror.Wrap(err, "Failed to connect to RabbitMQ")
	}

	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		s.isConnected = false
		return gerror.Wrap(err, "Failed to create RabbitMQ channel")
	}

	s.conn = conn
	s.channel = channel
	s.isConnected = true

	s.logger().Info(ctx, "RabbitMQ connection established successfully")
	return nil
}

// closeConnectionUnsafe 關閉連接（無鎖版本，內部使用）
func (s *sMessageQ) closeConnectionUnsafe() {
	// 關閉 channel
	if s.channel != nil {
		if err := s.channel.Close(); err != nil {
			s.logger().Warningf(context.Background(), "Error closing RabbitMQ channel: %v", err)
		}
		s.channel = nil
	}

	// 關閉連接
	if s.conn != nil {
		if err := s.conn.Close(); err != nil {
			s.logger().Warningf(context.Background(), "Error closing RabbitMQ connection: %v", err)
		}
		s.conn = nil
	}

	s.isConnected = false
	s.logger().Debug(context.Background(), "RabbitMQ connection resources cleaned up")
}

// maintainConnection maintains connection state
func (s *sMessageQ) maintainConnection(ctx context.Context) {
	// Initial connection
	if err := s.connect(ctx); err != nil {
		s.logger().Error(ctx, "Initial connection failed:", err)
		s.triggerReconnect()
	}

	for {
		select {
		case <-ctx.Done():
			s.logger().Info(ctx, "Connection maintenance goroutine exiting")
			return
		case <-s.closeChan:
			s.logger().Info(ctx, "Received close signal, connection maintenance goroutine exiting")
			return
		case <-s.reconnectChan:
			s.logger().Info(ctx, "Received reconnect signal, attempting to reconnect")
			s.reconnectWithRetry(ctx)
		}
	}
}

// reconnectWithRetry 重連邏輯，包含退避策略和限制機制
func (s *sMessageQ) reconnectWithRetry(ctx context.Context) {
	s.mu.Lock()
	s.reconnectCount++
	currentReconnectCount := s.reconnectCount
	s.mu.Unlock()

	// 檢查是否超過最大重連次數限制（防止無限重連）
	maxTotalReconnects := 100 // 總重連次數限制
	if currentReconnectCount > maxTotalReconnects {
		s.logger().Errorf(ctx, "Maximum total reconnection attempts (%d) exceeded, stopping reconnection", maxTotalReconnects)
		return
	}

	// 檢查重連頻率限制
	s.mu.RLock()
	timeSinceLastReconnect := time.Since(s.lastReconnectTime)
	s.mu.RUnlock()

	minReconnectInterval := time.Second * 10 // 最小重連間隔
	if timeSinceLastReconnect < minReconnectInterval {
		s.logger().Warningf(ctx, "Reconnection too frequent, waiting %v before retry", minReconnectInterval)
		time.Sleep(minReconnectInterval - timeSinceLastReconnect)
	}

	s.mu.Lock()
	s.lastReconnectTime = time.Now()
	s.mu.Unlock()

	// 實現指數退避算法
	for i := 0; i < s.maxRetries; i++ {
		if err := s.connect(ctx); err != nil {
			s.logger().Errorf(ctx, "Reconnection attempt %d/%d failed (total: %d): %v",
				i+1, s.maxRetries, currentReconnectCount, err)

			if i < s.maxRetries-1 {
				// 指數退避：基礎間隔 * 2^重試次數，但不超過最大間隔
				backoffMultiplier := int64(1 << uint(i))
				backoffInterval := time.Duration(int64(s.retryInterval) * backoffMultiplier)
				if backoffInterval > s.maxReconnectInterval {
					backoffInterval = s.maxReconnectInterval
				}

				s.logger().Infof(ctx, "Waiting %v before next reconnection attempt", backoffInterval)

				// 使用可中斷的等待
				select {
				case <-ctx.Done():
					s.logger().Info(ctx, "Context cancelled during reconnection backoff")
					return
				case <-time.After(backoffInterval):
					// 繼續重試
				}
			}
		} else {
			s.logger().Infof(ctx, "Reconnection successful after %d attempts (total reconnects: %d)",
				i+1, currentReconnectCount)
			// 重連成功後重置計數器
			s.mu.Lock()
			s.reconnectCount = 0
			s.mu.Unlock()
			return
		}
	}

	s.logger().Errorf(ctx, "Reconnection batch failed after %d attempts (total reconnects: %d)",
		s.maxRetries, currentReconnectCount)

	// 如果當前批次重連失敗，等待更長時間後再次嘗試
	// 但不會無限重連，而是有總次數限制
	if currentReconnectCount < maxTotalReconnects {
		extendedWaitTime := s.maxReconnectInterval
		s.logger().Infof(ctx, "Scheduling next reconnection batch in %v", extendedWaitTime)

		// 使用可中斷的等待
		select {
		case <-ctx.Done():
			s.logger().Info(ctx, "Context cancelled during extended wait")
			return
		case <-time.After(extendedWaitTime):
			s.triggerReconnect()
		}
	}
}

// startMessageConsumer starts message consumption
func (s *sMessageQ) startMessageConsumer(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			s.logger().Info(ctx, "Message consumer goroutine exiting")
			return
		case <-s.closeChan:
			s.logger().Info(ctx, "Received close signal, message consumer goroutine exiting")
			return
		default:
			if s.IsHealthy() {
				if err := s.consumeMessages(ctx); err != nil {
					s.logger().Error(ctx, "Message consumption failed:", err)
					s.triggerReconnect()
				}
			} else {
				// Wait for connection recovery
				time.Sleep(time.Second)
			}
		}
	}
}

// consumeMessages core logic for message consumption
func (s *sMessageQ) consumeMessages(ctx context.Context) error {
	s.mu.RLock()
	if !s.isConnected || s.channel == nil {
		s.mu.RUnlock()
		return gerror.New("RabbitMQ connection unavailable")
	}
	channel := s.channel
	s.mu.RUnlock()

	// Declare exchange
	err := channel.ExchangeDeclare(
		consts.ExchangeName,
		"direct",
		true,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return gerror.Wrap(err, "Failed to declare exchange")
	}

	// Declare queue
	queue, err := channel.QueueDeclare(
		"",
		true,
		true,
		true,
		false,
		nil,
	)
	if err != nil {
		return gerror.Wrap(err, "Failed to declare queue")
	}

	// Bind queue
	for _, key := range consts.RouteKeys {
		err = channel.QueueBind(queue.Name, key, consts.ExchangeName, false, nil)
		if err != nil {
			return gerror.Wrapf(err, "Failed to bind queue, key: %s", key)
		}
		s.logger().Infof(ctx, "Queue bound successfully, key: %s", key)
	}

	// Start consuming
	messages, err := channel.Consume(
		queue.Name,
		"",
		true,
		false,
		false,
		false,
		nil,
	)
	if err != nil {
		return gerror.Wrap(err, "Failed to start consuming")
	}

	s.logger().Info(ctx, "Started consuming messages")

	// Message consumption loop
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-s.closeChan:
			return nil
		case msg, ok := <-messages:
			if !ok {
				return gerror.New("Message channel closed")
			}
			s.dispatchMessage(ctx, msg)
		}
	}
}

// triggerReconnect triggers reconnection
func (s *sMessageQ) triggerReconnect() {
	select {
	case s.reconnectChan <- struct{}{}:
		// Reconnect signal sent successfully
	default:
		// Channel is full, reconnect signal already exists
	}
}

// IsHealthy 檢查連接健康狀態
func (s *sMessageQ) IsHealthy() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 如果已經關閉，則不健康
	if s.isClosed {
		return false
	}

	return s.isConnected &&
		s.conn != nil &&
		!s.conn.IsClosed() &&
		s.channel != nil
}

// Close 優雅關閉連接，防止重複關閉導致 panic
func (s *sMessageQ) Close() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 檢查是否已經關閉，防止重複關閉
	if s.isClosed {
		s.logger().Warning(context.Background(), "MessageQ already closed, skipping close operation")
		return nil
	}

	s.logger().Info(context.Background(), "Starting MessageQ graceful shutdown")

	// 標記為已關閉
	s.isClosed = true

	// 安全地關閉 closeChan
	select {
	case <-s.closeChan:
		// Channel 已經關閉
		s.logger().Debug(context.Background(), "Close channel already closed")
	default:
		// Channel 未關閉，安全關閉它
		close(s.closeChan)
		s.logger().Debug(context.Background(), "Close channel closed successfully")
	}

	// 關閉連接資源
	s.closeConnectionUnsafe()

	// 等待所有消息處理完成（最多等待 5 秒）
	s.waitForMessageProcessingComplete()

	s.logger().Info(context.Background(), "RabbitMQ connection closed successfully")
	return nil
}

// waitForMessageProcessingComplete 等待所有消息處理完成
func (s *sMessageQ) waitForMessageProcessingComplete() {
	timeout := time.Second * 5
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		// 檢查是否還有消息在處理中
		if len(s.messageWorkerSem) == 0 {
			// 所有 worker 都空閒，消息處理完成
			return
		}
		time.Sleep(time.Millisecond * 100)
	}

	s.logger().Warningf(context.Background(), "Timeout waiting for message processing to complete, %d workers still busy",
		len(s.messageWorkerSem))
}

// GetConnectionStatus 獲取連接狀態信息
func (s *sMessageQ) GetConnectionStatus() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	status := map[string]interface{}{
		"is_connected":           s.isConnected,
		"is_connecting":          s.isConnecting,
		"is_closed":              s.isClosed,
		"url":                    s.url,
		"max_retries":            s.maxRetries,
		"retry_interval":         s.retryInterval.String(),
		"max_reconnect_interval": s.maxReconnectInterval.String(),
		"reconnect_count":        s.reconnectCount,
		"last_reconnect_time":    s.lastReconnectTime.Format(time.RFC3339),
		"message_worker_count":   s.messageWorkerCount,
		"active_message_workers": len(s.messageWorkerSem),
	}

	if s.conn != nil {
		status["connection_closed"] = s.conn.IsClosed()
	}

	return status
}
