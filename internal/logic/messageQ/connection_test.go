package messageQ

import (
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
)

// 測試連接狀態管理
func TestConnectionManagement(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 初始狀態檢查
		t.<PERSON>sert(mq.IsHealthy(), false)
		t.<PERSON>sert(mq.isConnected, false)
		t.Assert(mq.isConnecting, false)
		
		// 檢查初始化參數
		t.Assert(mq.maxRetries, 5)
		t.Assert(mq.retryInterval, time.Second*5)
		t.AssertNE(mq.reconnectChan, nil)
		t.Assert<PERSON>(mq.closeChan, nil)
	})
}

// 測試連接狀態信息
func TestGetConnectionStatus(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		mq.url = "amqp://test:test@localhost:5672/"
		
		status := mq.GetConnectionStatus()
		
		// 檢查狀態信息
		t.Assert(status["is_connected"], false)
		t.<PERSON>ser<PERSON>(status["is_connecting"], false)
		t.Assert(status["url"], "amqp://test:test@localhost:5672/")
		t.Assert(status["max_retries"], 5)
		t.Assert(status["retry_interval"], "5s")
	})
}

// 測試重連觸發機制
func TestTriggerReconnect(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 測試重連信號發送
		mq.triggerReconnect()
		
		// 檢查通道中是否有信號
		select {
		case <-mq.reconnectChan:
			// 成功接收到重連信號
		case <-time.After(time.Millisecond * 100):
			t.Fatal("未收到重連信號")
		}
	})
}

// 測試優雅關閉
func TestClose(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)

		// 測試關閉操作
		err := mq.Close()
		t.AssertNil(err)

		// 檢查關閉信號通道是否已關閉
		select {
		case _, ok := <-mq.closeChan:
			if ok {
				t.Fatal("closeChan 應該已經關閉")
			}
			// 通道已關閉，這是正確的
		default:
			t.Fatal("closeChan 應該已經關閉並可讀取")
		}
	})
}

// 測試連接測試功能（模擬）
func TestTestConnection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		ctx := context.Background()
		
		// 測試無效 URL
		mq.url = "invalid://url"
		err := mq.testConnection(ctx)
		t.AssertNE(err, nil)
		
		// 測試空 URL
		mq.url = ""
		err = mq.testConnection(ctx)
		t.AssertNE(err, nil)
	})
}

// 測試連接狀態變更
func TestConnectionStateChanges(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 模擬連接狀態變更
		mq.mu.Lock()
		mq.isConnected = true
		mq.isConnecting = false
		mq.mu.Unlock()
		
		// 檢查健康狀態（沒有實際連接，所以仍然不健康）
		t.Assert(mq.IsHealthy(), false)
		
		// 檢查狀態信息
		status := mq.GetConnectionStatus()
		t.Assert(status["is_connected"], true)
		t.Assert(status["is_connecting"], false)
	})
}

// 測試並發安全性
func TestConcurrentAccess(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 並發讀取狀態
		done := make(chan bool, 10)
		
		for i := 0; i < 10; i++ {
			go func() {
				defer func() { done <- true }()
				
				// 並發調用各種方法
				mq.IsHealthy()
				mq.GetConnectionStatus()
				mq.triggerReconnect()
			}()
		}
		
		// 等待所有 goroutine 完成
		for i := 0; i < 10; i++ {
			select {
			case <-done:
				// 正常完成
			case <-time.After(time.Second):
				t.Fatal("並發測試超時")
			}
		}
	})
}

// 測試重連配置
func TestReconnectConfiguration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 檢查默認配置
		t.Assert(mq.maxRetries, 5)
		t.Assert(mq.retryInterval, time.Second*5)
		
		// 模擬修改配置
		mq.maxRetries = 3
		mq.retryInterval = time.Second * 2
		
		status := mq.GetConnectionStatus()
		t.Assert(status["max_retries"], 3)
		t.Assert(status["retry_interval"], "2s")
	})
}

// 測試資源清理
func TestResourceCleanup(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		
		// 模擬設置連接
		mq.mu.Lock()
		mq.isConnected = true
		// 注意：這裡不設置實際的連接對象，因為我們只是測試清理邏輯
		mq.mu.Unlock()
		
		// 調用內部清理方法
		mq.mu.Lock()
		mq.closeConnectionUnsafe()
		mq.mu.Unlock()
		
		// 檢查狀態是否正確重置
		t.Assert(mq.isConnected, false)
		t.Assert(mq.conn, nil)
		t.Assert(mq.channel, nil)
	})
}

// 測試錯誤處理
func TestErrorHandling(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		ctx := context.Background()
		
		// 測試無效配置的錯誤處理
		mq.url = "invalid-url"
		
		err := mq.testConnection(ctx)
		t.AssertNE(err, nil)
		t.AssertIN("Unable to connect to RabbitMQ", err.Error())
	})
}

// 基準測試 - 狀態檢查性能
func BenchmarkIsHealthy(b *testing.B) {
	mq := New().(*sMessageQ)
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mq.IsHealthy()
	}
}

// 基準測試 - 狀態信息獲取性能
func BenchmarkGetConnectionStatus(b *testing.B) {
	mq := New().(*sMessageQ)
	mq.url = "amqp://test:test@localhost:5672/"
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mq.GetConnectionStatus()
	}
}
