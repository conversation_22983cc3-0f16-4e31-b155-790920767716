package messageQ

import (
	"context"
	"dataSyncHub/internal/consts"
	"testing"
	"time"

	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	amqp "github.com/rabbitmq/amqp091-go"
)

// Test New function
func Test_New(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New()
		t.AssertNE(mq, nil)
		_, ok := mq.(*sMessageQ)
		t.Assert(ok, true)
	})
}

// Test RegisterHandler method
func Test_RegisterHandler(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)

		// messageHandlers should be empty on initialization
		t.Assert(len(mq.messageHandlers), 0)

		// Register a handler
		testHandler := func(ctx context.Context, message any) {}
		mq.RegisterHandler("test.prefix", testHandler)

		// Check if handler is registered
		t.<PERSON>sert(len(mq.messageHandlers), 1)
		_, exists := mq.messageHandlers["test.prefix"]
		t.Assert(exists, true)
	})
}

// Test dispatchMessage method
func Test_dispatchMessage(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		mq := New().(*sMessageQ)
		ctx := gctx.New()

		// Create a channel to check if handler is called
		handlerCalled := make(chan bool, 1)

		// Register a handler
		testHandler := func(ctx context.Context, message any) {
			handlerCalled <- true
		}
		mq.RegisterHandler("test.prefix", testHandler)

		// Create a mock message
		msg := amqp.Delivery{
			RoutingKey: "test.prefix.something",
		}

		// Dispatch message
		mq.dispatchMessage(ctx, msg)

		// Wait for handler to be called (with timeout)
		select {
		case called := <-handlerCalled:
			t.Assert(called, true)
		case <-time.After(time.Second * 2):
			t.Fatal("Handler was not called within timeout")
		}

		// Test non-matching message
		msg = amqp.Delivery{
			RoutingKey: "other.prefix.something",
		}

		// Dispatch message
		mq.dispatchMessage(ctx, msg)

		// Check if handler was not called (should timeout)
		select {
		case <-handlerCalled:
			t.Fatal("Handler should not be called for non-matching routing key")
		case <-time.After(time.Millisecond * 100):
			// Expected timeout, handler should not be called
		}
	})
}

// Test InitReceive method
func Test_InitReceive(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// Skip this test as it requires configuration access
		t.Skip("Skip this test as it requires configuration access")

		// Original test code below, but cannot run due to configuration issues
		/*
			ctx := gctx.New()

			mq := New().(*sMessageQ)

			// Register a handler
			handlerCalled := false
			testHandler := func(ctx context.Context, message any) {
				handlerCalled = true
			}
			mq.RegisterHandler(consts.RouteKeyMariadbPrefix, testHandler)

			// Initialize receive
			err := mq.InitReceive(ctx)
			t.Assert(err, nil)

			// Wait for connection establishment
			time.Sleep(2 * time.Second)

			// Check if connection is successfully established
			t.AssertNE(mq.conn, nil)
			t.AssertNE(mq.channel, nil)

			// Send a test message
			conn, err := amqp.Dial("amqp://admin:admin@127.0.0.1:5672/")
			if err == nil {
				defer conn.Close()

				ch, err := conn.Channel()
				if err == nil {
					defer ch.Close()

					// Publish message
					err = ch.PublishWithContext(
						ctx,
						consts.ExchangeName,
						consts.RouteKeyMariadbPrefix + consts.RouteKeyAMS,
						false,
						false,
						amqp.Publishing{
							ContentType: "text/plain",
							Body:        []byte("test message"),
						},
					)

					if err == nil {
						// Wait for message processing
						time.Sleep(2 * time.Second)
						// Check if handler was called
						t.Assert(handlerCalled, true)
					}
				}
			}
		*/
	})
}

// Test integration with actual RabbitMQ
func Test_RabbitMQIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// Skip this test if you don't want to run in CI environment or don't have RabbitMQ server
		t.Skip("Skip integration test, requires actual RabbitMQ server")

		ctx := gctx.New()

		// 連接到 RabbitMQ
		conn, err := amqp.Dial("amqp://admin:admin@127.0.0.1:5672/")
		if err != nil {
			t.Fatal("無法連接到 RabbitMQ:", err)
		}
		defer conn.Close()

		// 創建通道
		ch, err := conn.Channel()
		if err != nil {
			t.Fatal("無法創建通道:", err)
		}
		defer ch.Close()

		// 聲明交換機
		err = ch.ExchangeDeclare(
			consts.ExchangeName,
			"direct",
			true,
			false,
			false,
			false,
			nil,
		)
		t.Assert(err, nil)

		// 聲明隊列
		q, err := ch.QueueDeclare(
			"test_queue",
			false,
			true,
			false,
			false,
			nil,
		)
		t.Assert(err, nil)

		// 綁定隊列到交換機
		err = ch.QueueBind(
			q.Name,
			consts.RouteKeyMariadbPrefix+consts.RouteKeyAMS,
			consts.ExchangeName,
			false,
			nil,
		)
		t.Assert(err, nil)

		// 發布消息
		body := "test integration message"
		err = ch.PublishWithContext(
			ctx,
			consts.ExchangeName,
			consts.RouteKeyMariadbPrefix+consts.RouteKeyAMS,
			false,
			false,
			amqp.Publishing{
				ContentType: "text/plain",
				Body:        []byte(body),
			},
		)
		t.Assert(err, nil)

		// 消費消息
		msgs, err := ch.Consume(
			q.Name,
			"",
			true,
			false,
			false,
			false,
			nil,
		)
		t.Assert(err, nil)

		// 等待消息
		select {
		case msg := <-msgs:
			t.Assert(string(msg.Body), body)
			t.Assert(msg.RoutingKey, consts.RouteKeyMariadbPrefix+consts.RouteKeyAMS)
		case <-time.After(5 * time.Second):
			t.Fatal("超時：未收到消息")
		}
	})
}
