package vector

import (
	"dataSyncHub/internal/model"
	"testing"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate/entities/models"
)

// 創建一個模擬的 sVecWeaviate 結構體，用於測試
type mockVecWeaviate struct {
	VectorSettings      []*model.VectorCollectionSetting
	TenantToCollections g.MapStrAny
	mockData            map[string]interface{}
}

// 創建一個新的模擬 sVecWeaviate 實例
func newMockVecWeaviate() *mockVecWeaviate {
	mock := &mockVecWeaviate{
		VectorSettings:      make([]*model.VectorCollectionSetting, 0),
		TenantToCollections: make(g.MapStrAny),
		mockData:            make(map[string]interface{}),
	}

	// 初始化模擬數據
	mock.mockData["collections"] = []string{"test_collection"}
	mock.mockData["tenants"] = []string{"test_tenant"}
	mock.mockData["properties"] = []*models.Property{
		{
			Name:     "title",
			DataType: []string{"text"},
		},
		{
			Name:     "content",
			DataType: []string{"text"},
		},
	}

	return mock
}

// 實現 IVecWeaviate 接口的方法
func (m *mockVecWeaviate) CreateCollection(ctx interface{}, in model.CreateCollectionInput) (err error) {
	// 模擬創建集合的邏輯
	return nil
}

func (m *mockVecWeaviate) CreateData(ctx interface{}, in model.CreateDataInput) (out *model.CreateDataOutput, err error) {
	// 模擬創建數據的邏輯
	return &model.CreateDataOutput{
		IDs:     []string{"test-id-1", "test-id-2"},
		Total:   2,
		Success: 2,
		Fail:    0,
	}, nil
}

func (m *mockVecWeaviate) FetchRecords(ctx interface{}, in model.FetchRecordsInput) (out *model.FetchRecordsOutput, err error) {
	// 模擬獲取記錄的邏輯
	return &model.FetchRecordsOutput{
		Records: []g.Map{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
			},
			{
				"title":   "Test Title 2",
				"content": "Test Content 2",
				"id":      "test-id-2",
			},
		},
	}, nil
}

func (m *mockVecWeaviate) SimilaritySearch(ctx interface{}, in model.SimilaritySearchInput) (out *model.SimilaritySearchOutput, err error) {
	// 模擬相似性搜索的邏輯
	return &model.SimilaritySearchOutput{
		Properties: []map[string]any{
			{
				"title":    "Test Title 1",
				"content":  "Test Content 1",
				"id":       "test-id-1",
				"score":    0.95,
				"distance": 0.05,
			},
		},
	}, nil
}

func (m *mockVecWeaviate) HybridSearch(ctx interface{}, in model.HybridSearchInput) (out *model.HybridSearchOutput, err error) {
	// 模擬混合搜索的邏輯
	return &model.HybridSearchOutput{
		Properties: []map[string]any{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
				"score":   0.95,
			},
		},
	}, nil
}

func (m *mockVecWeaviate) UpdateProperties(ctx interface{}, in model.UpdatePropertiesInput) (err error) {
	// 模擬更新屬性的邏輯
	return nil
}

func (m *mockVecWeaviate) CreateTenantIfNotExist(ctx interface{}, in model.CreateTenantIfNotExistInput) (err error) {
	// 模擬創建租戶的邏輯
	return nil
}

func (m *mockVecWeaviate) DeleteTenants(ctx interface{}, in model.DeleteTenantsInput) (err error) {
	// 模擬刪除租戶的邏輯
	return nil
}

func (m *mockVecWeaviate) EmptyCollection(ctx interface{}, in model.EmptyCollectionInput) (err error) {
	// 模擬清空集合的邏輯
	return nil
}

// 實現其他必要的方法
func (m *mockVecWeaviate) OnMessage(ctx interface{}, message any) {
	// 模擬消息處理邏輯
}

func (m *mockVecWeaviate) UpdateVector(ctx interface{}, in model.UpdateVectorInput) (err error) {
	// 模擬更新向量的邏輯
	return nil
}

func (m *mockVecWeaviate) AddNewProperties(ctx interface{}, in model.AddNewPropertiesInput) (err error) {
	// 模擬添加新屬性的邏輯
	return nil
}

func (m *mockVecWeaviate) ClearDataByFilter(ctx interface{}, in model.ClearDataByFilterInput) (err error) {
	// 模擬按過濾器清除數據的邏輯
	return nil
}

func (m *mockVecWeaviate) DeleteCollection(ctx interface{}, collections []string) (err error) {
	// 模擬刪除集合的邏輯
	return nil
}

func (m *mockVecWeaviate) CreateSingleData(ctx interface{}, in model.CollectionData) (id string, err error) {
	// 模擬創建單條數據的邏輯
	return "test-id", nil
}

func (m *mockVecWeaviate) GetAllRecords(ctx interface{}, in model.GetAllRecordsInput) (out *model.GetAllRecordsOutput, err error) {
	// 模擬獲取所有記錄的邏輯
	return &model.GetAllRecordsOutput{
		Records: []map[string]any{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
			},
			{
				"title":   "Test Title 2",
				"content": "Test Content 2",
				"id":      "test-id-2",
			},
		},
	}, nil
}

func (m *mockVecWeaviate) GetPropertyNames(ctx interface{}, collection string) []string {
	// 模擬獲取屬性名稱的邏輯
	return []string{"title", "content"}
}

func (m *mockVecWeaviate) GetProperties(ctx interface{}, in model.GetPropertiesInput) (out *model.GetPropertiesOutput, err error) {
	// 模擬獲取屬性的邏輯
	return &model.GetPropertiesOutput{
		IDToProperties: map[string]map[string]any{
			"test-id-1": {
				"title":   "Test Title 1",
				"content": "Test Content 1",
			},
			"test-id-2": {
				"title":   "Test Title 2",
				"content": "Test Content 2",
			},
		},
	}, nil
}

func (m *mockVecWeaviate) GetPropertiesByGroup(ctx interface{}, in model.GetPropertiesByGroupInput) (out *model.GetPropertiesByGroupOutput, err error) {
	// 模擬按組獲取屬性的邏輯
	return &model.GetPropertiesByGroupOutput{
		OutputMap: map[string]string{
			"test-id-1": "Test Title 1",
			"test-id-2": "Test Title 2",
		},
		OutputData: []map[string]any{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
			},
			{
				"title":   "Test Title 2",
				"content": "Test Content 2",
				"id":      "test-id-2",
			},
		},
	}, nil
}

func (m *mockVecWeaviate) GetTenantAndCollections(ctx interface{}, collections []string) (out *g.MapStrAny, err error) {
	// 模擬獲取租戶和集合的邏輯
	result := g.MapStrAny{
		"test_tenant": []string{"test_collection"},
	}
	return &result, nil
}

// 測試 CreateCollection 方法
func TestCreateCollection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.CreateCollectionInput{
			VectorCollectionSetting: model.VectorCollectionSetting{
				CollectionName:   "test_collection",
				MultiTenancy:     true,
				VectorProperties: []string{"title", "content"},
				Properties: []*models.Property{
					{
						Name:     "title",
						DataType: []string{"text"},
					},
					{
						Name:     "content",
						DataType: []string{"text"},
					},
				},
			},
			Tenants:       []string{"test_tenant"},
			RenewSettings: true,
		}

		// 執行測試
		err := mock.CreateCollection(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
		t.Assert(len(mock.VectorSettings), 0) // 因為我們沒有實際執行更新設置的操作
	})
}

// 測試 CreateData 方法
func TestCreateData(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.CreateDataInput{
			Data: []*model.CollectionData{
				{
					ID:         "test-id-1",
					Tenant:     "test_tenant",
					Collection: "test_collection",
					Vector:     []float32{0.1, 0.2, 0.3},
					Properties: g.Map{
						"title":   "Test Title 1",
						"content": "Test Content 1",
					},
				},
				{
					ID:         "test-id-2",
					Tenant:     "test_tenant",
					Collection: "test_collection",
					Vector:     []float32{0.4, 0.5, 0.6},
					Properties: g.Map{
						"title":   "Test Title 2",
						"content": "Test Content 2",
					},
				},
			},
		}

		// 執行測試
		output, err := mock.CreateData(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
		// 驗證返回的結果與模擬方法返回的結果一致
		t.Assert(output.IDs, []string{"test-id-1", "test-id-2"})
		t.Assert(output.Total, 2)
		t.Assert(output.Success, 2)
		t.Assert(output.Fail, 0)
	})
}

// 測試 FetchRecords 方法
func TestFetchRecords(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.FetchRecordsInput{
			Tenant:     "test_tenant",
			Collection: "test_collection",
			Properties: []string{"title", "content", "id"},
			PageSize:   10,
			Offset:     0,
			Filter:     nil,
		}

		// 執行測試
		output, err := mock.FetchRecords(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
		// 驗證返回的結果與模擬方法返回的結果一致
		expectedRecords := []g.Map{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
			},
			{
				"title":   "Test Title 2",
				"content": "Test Content 2",
				"id":      "test-id-2",
			},
		}
		t.Assert(output.Records, expectedRecords)
	})
}

// 測試 SimilaritySearch 方法
func TestSimilaritySearch(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.SimilaritySearchInput{
			Tenant:     "test_tenant",
			Collection: "test_collection",
			Properties: []string{"title", "content", "id", "score", "distance"},
			Vector:     []float32{0.1, 0.2, 0.3},
			Distance:   0.8,
			Limit:      5,
			Filter:     nil,
		}

		// 執行測試
		output, err := mock.SimilaritySearch(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
		// 驗證返回的結果與模擬方法返回的結果一致
		expectedProperties := []map[string]any{
			{
				"title":    "Test Title 1",
				"content":  "Test Content 1",
				"id":       "test-id-1",
				"score":    0.95,
				"distance": 0.05,
			},
		}
		t.Assert(output.Properties, expectedProperties)
	})
}

// 測試 HybridSearch 方法
func TestHybridSearch(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		filter := filters.Where().
			WithPath([]string{"title"}).
			WithOperator(filters.Like).
			WithValueText("Test*")

		input := model.HybridSearchInput{
			Tenant:         "test_tenant",
			Collection:     "test_collection",
			Query:          "test query",
			Properties:     []string{"title", "content", "id", "score"},
			Vector:         []float32{0.1, 0.2, 0.3},
			Filter:         filter,
			Alpha:          0.5,
			PropertyWeight: []string{"title", "content"},
			FusionType:     "rankedFusion",
			Limit:          5,
		}

		// 執行測試
		output, err := mock.HybridSearch(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
		// 驗證返回的結果與模擬方法返回的結果一致
		expectedProperties := []map[string]any{
			{
				"title":   "Test Title 1",
				"content": "Test Content 1",
				"id":      "test-id-1",
				"score":   0.95,
			},
		}
		t.Assert(output.Properties, expectedProperties)
	})
}

// 測試 UpdateProperties 方法
func TestUpdateProperties(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.UpdatePropertiesInput{
			Tenant:     "test_tenant",
			Collection: "test_collection",
			ID:         "test-id-1",
			Properties: g.Map{
				"title":   "Updated Title",
				"content": "Updated Content",
			},
		}

		// 執行測試
		err := mock.UpdateProperties(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
	})
}

// 測試 CreateTenantIfNotExist 方法
func TestCreateTenantIfNotExist(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.CreateTenantIfNotExistInput{
			Tenant:      "new_tenant",
			Collections: []string{"test_collection"},
		}

		// 執行測試
		err := mock.CreateTenantIfNotExist(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
	})
}

// 測試 DeleteTenants 方法
func TestDeleteTenants(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.DeleteTenantsInput{
			Collections: []string{"test_collection"},
			Tenants:     []string{"test_tenant"},
		}

		// 執行測試
		err := mock.DeleteTenants(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
	})
}

// 測試 EmptyCollection 方法
func TestEmptyCollection(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		mock := newMockVecWeaviate()

		// 創建測試輸入
		input := model.EmptyCollectionInput{
			CollectionName: "test_collection",
			Tenant:         "test_tenant",
		}

		// 執行測試
		err := mock.EmptyCollection(ctx, input)

		// 驗證結果
		t.Assert(err, nil)
	})
}
