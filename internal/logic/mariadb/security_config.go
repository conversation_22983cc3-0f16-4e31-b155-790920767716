package mariadb

import (
	"context"
	"strings"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"
)

// SecurityConfig SQL security configuration management
type SecurityConfig struct {
	// SQL execution security configuration
	SQL SQLSecurityConfig `json:"sql"`

	// Audit configuration
	Audit AuditConfig `json:"audit"`

	// Rate limit configuration
	RateLimit RateLimitConfig `json:"rate_limit"`
}

// SQLSecurityConfig SQL security configuration
type SQLSecurityConfig struct {
	// Whether to enable SQL security validation
	Enabled bool `json:"enabled"`

	// Allowed SQL operation types
	AllowedOperations []string `json:"allowed_operations"`

	// Forbidden keywords
	ForbiddenKeywords []string `json:"forbidden_keywords"`

	// Maximum SQL statement length
	MaxStatementLength int `json:"max_statement_length"`

	// Whether to allow DDL operations
	AllowDDL bool `json:"allow_ddl"`

	// Whether to allow system table access
	AllowSystemTables bool `json:"allow_system_tables"`

	// Maximum batch execution statement count
	MaxBatchSize int `json:"max_batch_size"`

	// Whether to enable strict mode (stricter validation)
	StrictMode bool `json:"strict_mode"`
}

// AuditConfig audit configuration
type AuditConfig struct {
	// Whether to enable audit
	Enabled bool `json:"enabled"`

	// Whether to log successful operations
	LogSuccessOperations bool `json:"log_success_operations"`

	// Whether to log failed operations
	LogFailedOperations bool `json:"log_failed_operations"`

	// Whether to log sensitive operations
	LogSensitiveOperations bool `json:"log_sensitive_operations"`

	// Audit log retention days
	RetentionDays int `json:"retention_days"`
}

// RateLimitConfig rate limit configuration
type RateLimitConfig struct {
	// Whether to enable rate limiting
	Enabled bool `json:"enabled"`

	// Maximum requests per minute
	MaxRequestsPerMinute int `json:"max_requests_per_minute"`

	// Maximum requests per hour
	MaxRequestsPerHour int `json:"max_requests_per_hour"`

	// Maximum requests per day
	MaxRequestsPerDay int `json:"max_requests_per_day"`

	// Rate limit window size
	WindowSize time.Duration `json:"window_size"`
}

// GetSecurityConfig gets security configuration from config
func GetSecurityConfig(ctx context.Context) (*SecurityConfig, error) {
	// Try to get from Nacos configuration
	if cfg, err := g.Cfg().Get(ctx, "security.mariadb"); err == nil && !cfg.IsEmpty() {
		var config SecurityConfig
		if err := cfg.Struct(&config); err == nil {
			return &config, nil
		}
	}

	// Return default security configuration
	return GetDefaultSecurityConfig(), nil
}

// GetDefaultSecurityConfig gets default security configuration
func GetDefaultSecurityConfig() *SecurityConfig {
	return &SecurityConfig{
		SQL: SQLSecurityConfig{
			Enabled: false,
			AllowedOperations: []string{
				"SELECT", "INSERT", "UPDATE", "DELETE",
				"CREATE TABLE", "ALTER TABLE", "DROP TABLE",
				"CREATE INDEX", "DROP INDEX",
			},
			ForbiddenKeywords: []string{
				// System functions and dangerous operations
				"LOAD_FILE", "INTO OUTFILE", "INTO DUMPFILE",
				"SYSTEM", "EXEC", "EXECUTE",
				// User and permission management
				"CREATE USER", "DROP USER", "GRANT", "REVOKE",
				"SET PASSWORD", "ALTER USER",
				// System variable operations
				"SET GLOBAL", "SET SESSION",
				// Stored procedures and functions
				"CREATE PROCEDURE", "DROP PROCEDURE",
				"CREATE FUNCTION", "DROP FUNCTION",
				// Triggers
				"CREATE TRIGGER", "DROP TRIGGER",
				// Event scheduler
				"CREATE EVENT", "DROP EVENT",
				// Database operations
				"CREATE DATABASE", "DROP DATABASE",
				// Other dangerous operations
				"SHUTDOWN", "FLUSH", "RESET",
			},
			MaxStatementLength: 10000,
			AllowDDL:           true,
			AllowSystemTables:  false,
			MaxBatchSize:       100,
			StrictMode:         false,
		},
		Audit: AuditConfig{
			Enabled:                true,
			LogSuccessOperations:   false,
			LogFailedOperations:    true,
			LogSensitiveOperations: true,
			RetentionDays:          30,
		},
		RateLimit: RateLimitConfig{
			Enabled:              true,
			MaxRequestsPerMinute: 100,
			MaxRequestsPerHour:   1000,
			MaxRequestsPerDay:    10000,
			WindowSize:           time.Minute,
		},
	}
}

// GetQueryOnlyConfig gets query-only security configuration
func GetQueryOnlyConfig() *SecurityConfig {
	config := GetDefaultSecurityConfig()
	config.SQL.AllowedOperations = []string{"SELECT"}
	config.SQL.AllowDDL = false
	config.SQL.StrictMode = true
	return config
}

// GetDDLConfig gets DDL operation security configuration
func GetDDLConfig() *SecurityConfig {
	config := GetDefaultSecurityConfig()
	config.SQL.AllowedOperations = []string{
		"CREATE TABLE", "ALTER TABLE", "DROP TABLE",
		"CREATE INDEX", "DROP INDEX",
		"CREATE VIEW", "DROP VIEW",
	}
	config.SQL.AllowDDL = true
	config.SQL.StrictMode = true
	return config
}

// GetAdminConfig gets administrator-level security configuration
func GetAdminConfig() *SecurityConfig {
	config := GetDefaultSecurityConfig()
	config.SQL.AllowSystemTables = true
	config.SQL.ForbiddenKeywords = []string{
		// Only prohibit the most dangerous operations
		"SHUTDOWN", "LOAD_FILE", "INTO OUTFILE", "INTO DUMPFILE",
	}
	config.SQL.StrictMode = false
	return config
}

// ValidateSecurityConfig validates security configuration validity
func ValidateSecurityConfig(config *SecurityConfig) error {
	if config == nil {
		return gerror.New("Security configuration cannot be empty")
	}

	if config.SQL.MaxStatementLength <= 0 {
		return gerror.New("SQL statement maximum length must be greater than 0")
	}

	if config.SQL.MaxBatchSize <= 0 {
		return gerror.New("Batch execution maximum statement count must be greater than 0")
	}

	if config.Audit.RetentionDays <= 0 {
		return gerror.New("Audit log retention days must be greater than 0")
	}

	if config.RateLimit.Enabled {
		if config.RateLimit.MaxRequestsPerMinute <= 0 ||
			config.RateLimit.MaxRequestsPerHour <= 0 ||
			config.RateLimit.MaxRequestsPerDay <= 0 {
			return gerror.New("Rate limit configuration request count must be greater than 0")
		}
	}

	return nil
}

// ToSQLValidationConfig converts to SQL validation configuration
func (c *SecurityConfig) ToSQLValidationConfig() SQLValidationConfig {
	return SQLValidationConfig{
		AllowedOperations:  c.SQL.AllowedOperations,
		ForbiddenKeywords:  c.SQL.ForbiddenKeywords,
		MaxStatementLength: c.SQL.MaxStatementLength,
		AllowDDL:           c.SQL.AllowDDL,
		AllowSystemTables:  c.SQL.AllowSystemTables,
	}
}

// IsOperationAllowed checks if operation is allowed
func (c *SecurityConfig) IsOperationAllowed(operation string) bool {
	if !c.SQL.Enabled {
		return true
	}

	for _, allowed := range c.SQL.AllowedOperations {
		if allowed == operation {
			return true
		}
	}
	return false
}

// IsSensitiveOperation checks if it is a sensitive operation
func (c *SecurityConfig) IsSensitiveOperation(sql string) bool {
	sensitiveOperations := []string{
		"DROP", "DELETE", "TRUNCATE", "ALTER", "GRANT", "REVOKE",
		"CREATE USER", "DROP USER", "SET PASSWORD",
	}

	sqlUpper := strings.ToUpper(sql)
	for _, op := range sensitiveOperations {
		if gstr.Contains(sqlUpper, op) {
			return true
		}
	}
	return false
}
