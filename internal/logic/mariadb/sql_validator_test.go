package mariadb

import (
	"context"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

func TestSQLValidator_ValidateSQL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		validator := NewSQLValidator()
		config := validator.GetDefaultConfig()
		ctx := context.Background()

		// 測試安全的 SQL 語句
		sql := "SELECT * FROM users WHERE id = 1"
		err := validator.ValidateSQL(ctx, sql, config)
		t.AssertNil(err)

		sql = "INSERT INTO users (name, email) VALUES ('test', '<EMAIL>')"
		err = validator.ValidateSQL(ctx, sql, config)
		t.AssertNil(err)

		sql = "UPDATE users SET name = 'updated' WHERE id = 1"
		err = validator.ValidateSQL(ctx, sql, config)
		t.AssertNil(err)

		// 測試危險的 SQL 語句
		sql = "SELECT LOAD_FILE('/etc/passwd')"
		err = validator.ValidateSQL(ctx, sql, config)
		t.AssertNE(err, nil)

		sql = "DROP USER 'test'@'localhost'"
		err = validator.ValidateSQL(ctx, sql, config)
		t.AssertNE(err, nil)

		sql = "SELECT * FROM information_schema.tables"
		err = validator.ValidateSQL(ctx, sql, config)
		t.AssertNE(err, nil)

		err = validator.ValidateSQL(ctx, "", config)
		t.AssertNE(err, nil)
	})
}

func TestSQLValidator_ValidateBatchSQL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		validator := NewSQLValidator()
		config := validator.GetDefaultConfig()
		ctx := context.Background()

		// 安全的批量 SQL
		sqlList := []string{
			"SELECT * FROM users WHERE id = 1",
			"INSERT INTO users (name) VALUES ('test')",
			"UPDATE users SET name = 'updated' WHERE id = 1",
		}
		err := validator.ValidateBatchSQL(ctx, sqlList, config)
		t.AssertNil(err)

		// 包含危險 SQL 的批量
		sqlList = []string{
			"SELECT * FROM users WHERE id = 1",
			"DROP TABLE users",
			"INSERT INTO users (name) VALUES ('test')",
		}
		err = validator.ValidateBatchSQL(ctx, sqlList, config)
		t.AssertNE(err, nil)

		// 空批量 SQL
		err = validator.ValidateBatchSQL(ctx, []string{}, config)
		t.AssertNE(err, nil)
	})
}

func TestSQLValidator_ExtractSQLOperation(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		validator := NewSQLValidator()

		result := validator.extractSQLOperation("SELECT * FROM users")
		t.AssertEQ(result, "SELECT")

		result = validator.extractSQLOperation("CREATE TABLE test (id INT)")
		t.AssertEQ(result, "CREATE TABLE")

		result = validator.extractSQLOperation("")
		t.AssertEQ(result, "UNKNOWN")
	})
}
