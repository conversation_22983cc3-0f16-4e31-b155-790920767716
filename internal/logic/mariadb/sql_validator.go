package mariadb

import (
	"context"
	"dataSyncHub/internal/consts"
	"fmt"
	"regexp"
	"strings"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
)

// SQLValidator SQL 安全驗證器
type SQLValidator struct {
	logger glog.ILogger
}

// NewSQLValidator 創建新的 SQL 驗證器
func NewSQLValidator() *SQLValidator {
	return &SQLValidator{
		logger: g.Log().Cat(consts.CatDB),
	}
}

// SQLValidationConfig SQL 驗證配置
type SQLValidationConfig struct {
	AllowedOperations []string // 允許的 SQL 操作類型
	ForbiddenKeywords []string // 禁止的關鍵字
	MaxStatementLength int     // SQL 語句最大長度
	AllowDDL          bool     // 是否允許 DDL 操作
	AllowSystemTables bool     // 是否允許訪問系統表
}

// GetDefaultConfig 獲取默認安全配置
func (v *SQLValidator) GetDefaultConfig() SQLValidationConfig {
	return SQLValidationConfig{
		AllowedOperations: []string{
			"SELECT", "INSERT", "UPDATE", "DELETE",
			"CREATE TABLE", "ALTER TABLE", "DROP TABLE",
			"CREATE INDEX", "DROP INDEX",
		},
		ForbiddenKeywords: []string{
			// 系統函數和危險操作
			"LOAD_FILE", "INTO OUTFILE", "INTO DUMPFILE",
			"SYSTEM", "EXEC", "EXECUTE",
			// 用戶和權限管理
			"CREATE USER", "DROP USER", "GRANT", "REVOKE",
			"SET PASSWORD", "ALTER USER",
			// 系統變量操作
			"SET GLOBAL", "SET SESSION",
			// 存儲過程和函數
			"CREATE PROCEDURE", "DROP PROCEDURE",
			"CREATE FUNCTION", "DROP FUNCTION",
			// 觸發器
			"CREATE TRIGGER", "DROP TRIGGER",
			// 事件調度器
			"CREATE EVENT", "DROP EVENT",
			// 數據庫操作
			"CREATE DATABASE", "DROP DATABASE",
			// 其他危險操作
			"SHUTDOWN", "FLUSH", "RESET",
		},
		MaxStatementLength: 10000, // 10KB
		AllowDDL:          true,
		AllowSystemTables: false,
	}
}

// ValidateSQL 驗證 SQL 語句的安全性
func (v *SQLValidator) ValidateSQL(ctx context.Context, sql string, config SQLValidationConfig) error {
	if sql == "" {
		return gerror.New("SQL statement cannot be empty")
	}

	// 1. 檢查 SQL 語句長度
	if len(sql) > config.MaxStatementLength {
		v.logger.Warningf(ctx, "SQL statement too long: %d characters", len(sql))
		return gerror.Newf("SQL statement length exceeds limit (%d characters)", config.MaxStatementLength)
	}

	// 2. 清理和標準化 SQL
	cleanSQL := v.cleanSQL(sql)
	
	// 3. 檢查是否包含禁止的關鍵字
	if err := v.checkForbiddenKeywords(ctx, cleanSQL, config.ForbiddenKeywords); err != nil {
		return err
	}

	// 4. 檢查 SQL 操作類型
	if err := v.checkAllowedOperations(ctx, cleanSQL, config.AllowedOperations); err != nil {
		return err
	}

	// 5. 檢查系統表訪問
	if !config.AllowSystemTables {
		if err := v.checkSystemTableAccess(ctx, cleanSQL); err != nil {
			return err
		}
	}

	// 6. 檢查 SQL 注入模式
	if err := v.checkSQLInjectionPatterns(ctx, cleanSQL); err != nil {
		return err
	}

	// 7. 檢查 DDL 操作
	if !config.AllowDDL {
		if err := v.checkDDLOperations(ctx, cleanSQL); err != nil {
			return err
		}
	}

	return nil
}

// cleanSQL 清理和標準化 SQL 語句
func (v *SQLValidator) cleanSQL(sql string) string {
	// 移除多餘的空白字符
	sql = strings.TrimSpace(sql)
	sql = regexp.MustCompile(`\s+`).ReplaceAllString(sql, " ")
	
	// 移除 SQL 註釋
	sql = regexp.MustCompile(`--.*`).ReplaceAllString(sql, "")
	sql = regexp.MustCompile(`/\*.*?\*/`).ReplaceAllString(sql, "")
	
	return strings.ToUpper(sql)
}

// checkForbiddenKeywords 檢查禁止的關鍵字
func (v *SQLValidator) checkForbiddenKeywords(ctx context.Context, sql string, forbidden []string) error {
	for _, keyword := range forbidden {
		if gstr.ContainsI(sql, keyword) {
			v.logger.Warningf(ctx, "Detected forbidden keyword: %s", keyword)
			return gerror.Newf("SQL statement contains forbidden keyword: %s", keyword)
		}
	}
	return nil
}

// checkAllowedOperations 檢查允許的操作類型
func (v *SQLValidator) checkAllowedOperations(ctx context.Context, sql string, allowed []string) error {
	// Extract SQL operation type
	operation := v.extractSQLOperation(sql)
	if operation == "" {
		return gerror.New("Unable to identify SQL operation type")
	}

	// Check if in allowed list
	for _, allowedOp := range allowed {
		if strings.HasPrefix(sql, allowedOp) {
			return nil
		}
	}

	v.logger.Warningf(ctx, "Disallowed SQL operation: %s", operation)
	return gerror.Newf("Disallowed SQL operation: %s", operation)
}

// extractSQLOperation 提取 SQL 操作類型
func (v *SQLValidator) extractSQLOperation(sql string) string {
	words := strings.Fields(sql)
	if len(words) == 0 {
		return ""
	}

	// 處理複合操作
	if len(words) >= 2 {
		twoWords := fmt.Sprintf("%s %s", words[0], words[1])
		compoundOps := []string{
			"CREATE TABLE", "CREATE INDEX", "CREATE USER", "CREATE DATABASE",
			"DROP TABLE", "DROP INDEX", "DROP USER", "DROP DATABASE",
			"ALTER TABLE", "SET GLOBAL", "SET SESSION",
		}
		for _, op := range compoundOps {
			if strings.HasPrefix(sql, op) {
				return twoWords
			}
		}
	}

	return words[0]
}

// checkSystemTableAccess 檢查系統表訪問
func (v *SQLValidator) checkSystemTableAccess(ctx context.Context, sql string) error {
	systemTables := []string{
		"INFORMATION_SCHEMA", "PERFORMANCE_SCHEMA", "MYSQL", "SYS",
		"USER", "DB", "HOST", "TABLES_PRIV", "COLUMNS_PRIV",
	}

	for _, table := range systemTables {
		if gstr.ContainsI(sql, table) {
			v.logger.Warningf(ctx, "Attempting to access system table: %s", table)
			return gerror.Newf("Access to system table not allowed: %s", table)
		}
	}
	return nil
}

// checkSQLInjectionPatterns 檢查常見的 SQL 注入模式
func (v *SQLValidator) checkSQLInjectionPatterns(ctx context.Context, sql string) error {
	// Common SQL injection patterns
	patterns := []string{
		`UNION\s+SELECT`,
		`OR\s+1\s*=\s*1`,
		`AND\s+1\s*=\s*1`,
		`'\s*OR\s*'`,
		`"\s*OR\s*"`,
		`;\s*DROP`,
		`;\s*DELETE`,
		`;\s*UPDATE`,
		`;\s*INSERT`,
		`BENCHMARK\s*\(`,
		`SLEEP\s*\(`,
		`WAITFOR\s+DELAY`,
	}

	for _, pattern := range patterns {
		matched, err := regexp.MatchString(pattern, sql)
		if err != nil {
			continue
		}
		if matched {
			v.logger.Warningf(ctx, "Detected suspicious SQL injection pattern: %s", pattern)
			return gerror.New("Detected suspicious SQL injection pattern")
		}
	}
	return nil
}

// checkDDLOperations 檢查 DDL 操作
func (v *SQLValidator) checkDDLOperations(ctx context.Context, sql string) error {
	ddlOperations := []string{
		"CREATE", "ALTER", "DROP", "TRUNCATE", "RENAME",
	}

	for _, op := range ddlOperations {
		if strings.HasPrefix(sql, op) {
			v.logger.Warningf(ctx, "Detected DDL operation: %s", op)
			return gerror.Newf("DDL operation not allowed: %s", op)
		}
	}
	return nil
}

// ValidateBatchSQL 批量驗證 SQL 語句
func (v *SQLValidator) ValidateBatchSQL(ctx context.Context, sqlList []string, config SQLValidationConfig) error {
	if len(sqlList) == 0 {
		return gerror.New("SQL statement list cannot be empty")
	}

	// Check batch execution statement count limit
	maxBatchSize := 100
	if len(sqlList) > maxBatchSize {
		return gerror.Newf("Batch execution statement count exceeds limit (%d)", maxBatchSize)
	}

	// Validate each SQL statement individually
	for i, sql := range sqlList {
		if err := v.ValidateSQL(ctx, sql, config); err != nil {
			return gerror.Newf("SQL statement #%d validation failed: %v", i+1, err)
		}
	}

	return nil
}

// LogSecurityEvent logs security events
func (v *SQLValidator) LogSecurityEvent(ctx context.Context, event string, sql string, clientIP string) {
	v.logger.Warningf(ctx, "SQL security event: %s, SQL: %s, Client IP: %s", event, sql, clientIP)
}
