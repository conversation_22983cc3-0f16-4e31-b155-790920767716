package mariadb

import (
	"context"
	"dataSyncHub/internal/consts"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/test/gtest"
	"testing"
)

// TestSchemaCacheOperations 測試 schema 緩存操作
func TestSchemaCacheOperations(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		s := New().(*sMariaDB)

		// 清理測試數據
		defer func() {
			g.Redis().Del(ctx, consts.CacheKeySchemas)
		}()

		testSchema := "test_schema"

		// 測試緩存未命中
		exists, fromCache := s.isSchemaExistsInCache(ctx, testSchema)
		t.Assert(fromCache, true) // 應該能正常訪問 Redis
		t.Assert(exists, false)   // 應該不存在

		// 測試添加到緩存
		err := s.addSchemaToCache(ctx, testSchema)
		t.AssertNil(err)

		// 測試緩存命中
		exists, fromCache = s.isSchemaExistsInCache(ctx, testSchema)
		t.Assert(fromCache, true) // 應該能正常訪問 Redis
		t.Assert(exists, true)    // 應該存在

		// 測試緩存統計
		stats := s.getCacheStats(ctx)
		t.Assert(stats["schema_count"], 1)
	})
}

// TestTableCacheOperations 測試 table 緩存操作
func TestTableCacheOperations(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		s := New().(*sMariaDB)

		testSchema := "test_schema"
		testTable := "test_table"

		// 清理測試數據
		defer func() {
			g.Redis().Del(ctx, consts.CacheKeySchemas)
			g.Redis().Del(ctx, fmt.Sprintf(consts.CacheKeyTables, testSchema))
		}()

		// 測試緩存未命中
		exists, fromCache := s.isTableExistsInCache(ctx, testSchema, testTable)
		t.Assert(fromCache, true) // 應該能正常訪問 Redis
		t.Assert(exists, false)   // 應該不存在

		// 測試添加到緩存
		err := s.addTableToCache(ctx, testSchema, testTable)
		t.AssertNil(err)

		// 測試緩存命中
		exists, fromCache = s.isTableExistsInCache(ctx, testSchema, testTable)
		t.Assert(fromCache, true) // 應該能正常訪問 Redis
		t.Assert(exists, true)    // 應該存在
	})
}

// TestCacheClearOperations 測試緩存清理操作
func TestCacheClearOperations(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		s := New().(*sMariaDB)

		testSchema := "test_schema"
		testTable := "test_table"

		// 添加測試數據
		s.addSchemaToCache(ctx, testSchema)
		s.addTableToCache(ctx, testSchema, testTable)

		// 驗證數據存在
		exists, _ := s.isSchemaExistsInCache(ctx, testSchema)
		t.Assert(exists, true)

		tableExists, _ := s.isTableExistsInCache(ctx, testSchema, testTable)
		t.Assert(tableExists, true)

		// 清理 table 緩存
		err := s.clearTableCache(ctx, testSchema)
		t.AssertNil(err)

		// 驗證 table 緩存已清理
		tableExists, _ = s.isTableExistsInCache(ctx, testSchema, testTable)
		t.Assert(tableExists, false)

		// 但 schema 緩存應該還在
		exists, _ = s.isSchemaExistsInCache(ctx, testSchema)
		t.Assert(exists, true)

		// 清理 schema 緩存
		err = s.clearSchemaCache(ctx)
		t.AssertNil(err)

		// 驗證 schema 緩存已清理
		exists, _ = s.isSchemaExistsInCache(ctx, testSchema)
		t.Assert(exists, false)
	})
}
