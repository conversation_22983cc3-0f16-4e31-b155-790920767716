package settings

import (
	"context"
	"dataSyncHub/boot"
	"dataSyncHub/internal/dao"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/model/entity"
	"dataSyncHub/internal/service"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
)

func init() {
	service.RegisterSettings(New())
}

type sSettings struct {
}

func New() service.ISettings {
	boot.WaitReady()
	s := &sSettings{}
	ctx := gctx.GetInitCtx()
	if err := s.InitializeDatabase(ctx); err != nil {
		g.Log().Fatal(context.Background(), err)
	}

	return s
}

func (s *sSettings) InitializeDatabase(ctx context.Context) error {

	db := g.DB()

	// Create database if not exists
	_, err := db.Exec(ctx, "CREATE DATABASE IF NOT EXISTS dsh")
	if err != nil {
		return fmt.Errorf("failed to create database: %v", err)
	}

	// Switch to dsh database
	_, err = db.Exec(ctx, "USE dsh")
	if err != nil {
		return fmt.Errorf("failed to switch database: %v", err)
	}

	// Create settings table if not exists
	_, err = db.Exec(ctx, `
		CREATE TABLE IF NOT EXISTS settings (
			setting_name VARCHAR(255) PRIMARY KEY,
			settings TEXT NOT NULL 
		)`)
	if err != nil {
		return fmt.Errorf("failed to create settings table: %v", err)
	}

	return nil
}

func (s *sSettings) Get(ctx context.Context, in model.GetSettingsInput) (value string, err error) {
	var settings *entity.Settings
	if err = dao.Settings.Ctx(ctx).Where(dao.Settings.Columns().SettingName, in.SettingName).Scan(&settings); err != nil {
		return
	}
	if settings == nil {
		err = gerror.New("setting not found")
		return
	}

	value = settings.Settings

	return
}

func (s *sSettings) Set(ctx context.Context, in model.SetSettingsInput) (err error) {

	if err = g.Validator().Data(in).Run(ctx); err != nil {
		return
	}
	if !gjson.Valid(in.Settings) {
		err = gerror.New("settings is not valid json")
		return
	}

	err = dao.Settings.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		if _, e := tx.Model(dao.Settings.Table()).Replace(
			g.Map{
				dao.Settings.Columns().SettingName: in.SettingName,
				dao.Settings.Columns().Settings:    in.Settings,
			},
		); e != nil {
			return e
		}
		return nil
	})
	return
}
