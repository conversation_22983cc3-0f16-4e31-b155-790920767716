package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) GetTenantAndCollections(ctx context.Context, req *v1.GetTenantAndCollectionsReq) (res *v1.GetTenantAndCollectionsRes, err error) {
	res = &v1.GetTenantAndCollectionsRes{
		TenantCollections: make(g.Map),
	}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	var out *gmap.StrAnyMap
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().GetTenantAndCollections(ctx, req.Collections)

	})
	if err == nil {
		res.TenantCollections = out.Map()

	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)

	return
}
