package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) GetAllRecords(ctx context.Context, req *v1.GetAllRecordsReq) (res *v1.GetAllRecordsRes, err error) {
	res = &v1.GetAllRecordsRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	res.Records = make([]map[string]any, 0)
	var out *model.GetAllRecordsOutput
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().GetAllRecords(ctx, req.GetAllRecordsInput)

	})

	if err == nil {
		res.Records = out.Records
	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
