package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) GetProperties(ctx context.Context, req *v1.GetPropertiesReq) (res *v1.GetPropertiesRes, err error) {
	res = &v1.GetPropertiesRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.IDToProperties = make(map[string]map[string]any)
	var out *model.GetPropertiesOutput
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().GetProperties(ctx, req.GetPropertiesInput)
	})
	if err == nil {
		res.IDToProperties = out.IDToProperties
	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJsonExit(res)

	return
}
