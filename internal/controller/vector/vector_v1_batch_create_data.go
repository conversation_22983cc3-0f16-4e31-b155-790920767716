package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) BatchCreateData(ctx context.Context, req *v1.BatchCreateDataReq) (res *v1.BatchCreateDataRes, err error) {
	res = &v1.BatchCreateDataRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	var out *model.CreateDataOutput
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().CreateData(ctx, &req.CreateDataInput)
	})
	if err == nil {
		res.CreateDataOutput = *out

	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}

	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
