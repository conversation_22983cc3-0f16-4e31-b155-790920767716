package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) FetchRecords(ctx context.Context, req *v1.FetchRecordsReq) (res *v1.FetchRecordsRes, err error) {
	res = &v1.FetchRecordsRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Records = make([]g.Map, 0)
	var out *model.FetchRecordsOutput
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().FetchRecords(ctx, req.FetchRecordsInput)
	})

	if err == nil {
		res.Records = out.Records
	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return

}
