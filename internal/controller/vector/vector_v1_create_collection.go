package vector

import (
	"context"
	"dataSyncHub/api/vector/v1"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerV1) CreateCollection(ctx context.Context, req *v1.CreateCollectionReq) (res *v1.CreateCollectionRes, err error) {
	res = &v1.CreateCollectionRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	cost := gtime.FuncCost(func() {
		err = service.VecWeaviate().CreateCollection(ctx, req.CreateCollectionInput)
	})

	if err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}

	res.Cost = cost.String()

	g.RequestFromCtx(ctx).Response.WriteJsonExit(res)

	return
}
