package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) GetPropertyNames(ctx context.Context, req *v1.GetPropertyNamesReq) (res *v1.GetPropertyNamesRes, err error) {
	res = &v1.GetPropertyNamesRes{}
	res.Names = make([]string, 0)
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	cost := gtime.FuncCost(func() {
		res.Names = service.VecWeaviate().GetPropertyNames(ctx, req.Collection)
	})
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJsonExit(res)
	return

}
