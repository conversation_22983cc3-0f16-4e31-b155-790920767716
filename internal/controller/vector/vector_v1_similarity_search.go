package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) SimilaritySearch(ctx context.Context, req *v1.SimilaritySearchReq) (res *v1.SimilaritySearchRes, err error) {
	res = &v1.SimilaritySearchRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	res.Properties = make([]map[string]any, 0)
	var out *model.SimilaritySearchOutput
	cost := gtime.FuncCost(func() {
		out, err = service.VecWeaviate().SimilaritySearch(ctx, req.SimilaritySearchInput)

	})

	if err == nil {
		res.Properties = out.Properties
	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return

}
