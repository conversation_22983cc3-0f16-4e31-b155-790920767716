package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) GetPropertiesByGroup(ctx context.Context, req *v1.GetPropertiesByGroupReq) (res *v1.GetPropertiesByGroupRes, err error) {
	res = &v1.GetPropertiesByGroupRes{}
	res.OutputData = make([]map[string]any, 0)
	res.OutputMap = make(map[string]string)
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	var out *model.GetPropertiesByGroupOutput
	cost := gtime.FuncCost(func() {

		out, err = service.VecWeaviate().GetPropertiesByGroup(ctx, req.GetPropertiesByGroupInput)
	})
	if err == nil {
		res.OutputData = out.OutputData
		res.OutputMap = out.OutputMap

	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
