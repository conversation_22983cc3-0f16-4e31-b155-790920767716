package vector

import (
	"context"
	"dataSyncHub/api/vector/v1"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerV1) HybridSearch(ctx context.Context, req *v1.HybridSearchReq) (res *v1.HybridSearchRes, err error) {
	res = &v1.HybridSearchRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	res.Properties = make([]map[string]any, 0)
	var out *model.HybridSearchOutput
	cost := gtime.FuncCost(func() {

		out, err = service.VecWeaviate().HybridSearch(ctx, req.HybridSearchInput)
	})

	if err == nil {
		res.Properties = out.Properties
	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
