package vector

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/vector/v1"
)

func (c *ControllerV1) CreateData(ctx context.Context, req *v1.CreateDataReq) (res *v1.CreateDataRes, err error) {
	res = &v1.CreateDataRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	var id = ""
	cost := gtime.FuncCost(func() {
		id, err = service.VecWeaviate().CreateSingleData(ctx, req.CollectionData)

	})

	if err == nil {
		res.ID = id

	} else {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
