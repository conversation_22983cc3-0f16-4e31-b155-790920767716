package executeapi

import (
	"context"
	"dataSyncHub/api/executeapi/v1"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerV1) ExecuteSQL(ctx context.Context, req *v1.ExecuteSQLReq) (res *v1.ExecuteSQLRes, err error) {
	res = &v1.ExecuteSQLRes{
		Code:    consts.Success.Code(),
		Message: consts.Success.Message(),
	}
	var out *model.ExecuteSQLOutput
	cost := gtime.FuncCost(func() {
		out, err = service.MariaDB().ExecuteSQL(
			ctx,
			model.ExecuteSQLInput{
				Schema: req.Schema,
				RawSQL: req.RawSQL,
			},
		)
	})
	if err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}

	if !out.Success {
		res.Code = consts.Failed.Code()
		res.Message = out.Message
	}
	res.Cost = cost.String()

	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
