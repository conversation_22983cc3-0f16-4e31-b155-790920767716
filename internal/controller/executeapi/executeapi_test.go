package executeapi_test

import (
	"context"
	"dataSyncHub/api/executeapi/v1"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/controller/executeapi"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"errors"
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
)

// 在测试开始前注册一个默认的 MariaDB 服务实现
func init() {
	// 如果 MariaDB 服务还没有被注册，则注册一个默认的实现
	defer func() {
		if r := recover(); r != nil {
			// 捕获 panic，注册一个默认的 MariaDB 服务实现
			service.RegisterMariaDB(&MockMariaDB{})
		}
	}()

	// 尝试获取 MariaDB 服务，如果没有注册会 panic
	_ = service.MariaDB()
}

// 创建一个 Mock 版本的控制器，用于测试
type MockControllerV1 struct {
	executeapi.ControllerV1
}

// 重写 ExecuteSQL 方法，不调用 WriteJson
func (c *MockControllerV1) ExecuteSQL(ctx context.Context, req *v1.ExecuteSQLReq) (res *v1.ExecuteSQLRes, err error) {
	res = &v1.ExecuteSQLRes{
		Code:    consts.Success.Code(),
		Message: consts.Success.Message(),
	}

	// 参数验证
	if req.Schema == "" || req.RawSQL == "" {
		res.Code = consts.Failed.Code()
		res.Message = "参数错误"
		return
	}

	if out, err := service.MariaDB().ExecuteSQL(
		ctx,
		model.ExecuteSQLInput{
			Schema: req.Schema,
			RawSQL: req.RawSQL,
		},
	); err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	} else if !out.Success {
		res.Code = consts.Failed.Code()
		res.Message = out.Message
	}

	return
}

// 重写 BatchExecuteSQL 方法，不调用 WriteJson
func (c *MockControllerV1) BatchExecuteSQL(ctx context.Context, req *v1.BatchExecuteSQLReq) (res *v1.BatchExecuteSQLRes, err error) {
	res = &v1.BatchExecuteSQLRes{
		Code:    consts.Success.Code(),
		Message: consts.Success.Message(),
	}

	// 参数验证
	if req.Schema == "" || len(req.SQLList) == 0 {
		res.Code = consts.Failed.Code()
		res.Message = "error parameters"
		return
	}

	if out, err := service.MariaDB().BatchExecuteSQL(
		ctx,
		model.BatchExecuteSQLInput{
			Schema:  req.Schema,
			SQLList: req.SQLList,
		},
	); err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	} else {
		res.TotalCount = out.TotalCount
		res.Errors = out.Errors
		res.FailCount = out.FailCount
		res.SuccessCount = out.SuccessCount
		if !out.Success {
			res.Code = consts.Failed.Code()
		}
	}

	return
}

// 创建 MariaDB 服务的 Mock
type MockMariaDB struct {
	ExecuteSQLFunc      func(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error)
	BatchExecuteSQLFunc func(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error)
}

func (m *MockMariaDB) ExecuteSQL(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error) {
	if m.ExecuteSQLFunc != nil {
		return m.ExecuteSQLFunc(ctx, in)
	}
	return &model.ExecuteSQLOutput{Success: true, Message: "success"}, nil
}

func (m *MockMariaDB) BatchExecuteSQL(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
	if m.BatchExecuteSQLFunc != nil {
		return m.BatchExecuteSQLFunc(ctx, in)
	}
	return &model.BatchExecuteSQLOutput{Success: true, TotalCount: len(in.SQLList), SuccessCount: len(in.SQLList)}, nil
}

func (m *MockMariaDB) OnMessage(ctx context.Context, message any) {
	// 不需要实现
}

func (m *MockMariaDB) GetContents(ctx context.Context, in model.GetContentsReq) (out *model.GetContentsRes, err error) {
	// 不需要实现
	return nil, nil
}

// 测试 ExecuteSQL 方法 - 成功执行 SQL
func Test_ExecuteSQL_Success(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.ExecuteSQLFunc = func(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error) {
			t.Assert(in.Schema, "test_schema")
			t.Assert(in.RawSQL, "CREATE TABLE test (id INT)")
			return &model.ExecuteSQLOutput{
				Success: true,
				Message: "success",
			}, nil
		}

		// 执行测试
		req := &v1.ExecuteSQLReq{
			Schema: "test_schema",
			RawSQL: "CREATE TABLE test (id INT)",
		}
		res, err := controller.ExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Success.Code())
		t.Assert(res.Message, consts.Success.Message())
	})
}

// 测试 ExecuteSQL 方法 - 执行 SQL 失败
func Test_ExecuteSQL_Failure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.ExecuteSQLFunc = func(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error) {
			t.Assert(in.Schema, "test_schema")
			t.Assert(in.RawSQL, "INVALID SQL")
			return &model.ExecuteSQLOutput{
				Success: false,
				Message: "SQL syntax error",
			}, nil
		}

		// 执行测试
		req := &v1.ExecuteSQLReq{
			Schema: "test_schema",
			RawSQL: "INVALID SQL",
		}
		res, err := controller.ExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
		t.Assert(res.Message, "SQL syntax error")
	})
}

// 测试 BatchExecuteSQL 方法 - 所有 SQL 执行成功
func Test_BatchExecuteSQL_AllSuccess(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.BatchExecuteSQLFunc = func(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
			t.Assert(in.Schema, "test_schema")
			t.Assert(in.SQLList, []string{
				"CREATE TABLE test1 (id INT)",
				"CREATE TABLE test2 (id INT)",
			})
			return &model.BatchExecuteSQLOutput{
				Success:      true,
				TotalCount:   2,
				SuccessCount: 2,
				FailCount:    0,
				Errors:       []string{},
			}, nil
		}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema: "test_schema",
			SQLList: []string{
				"CREATE TABLE test1 (id INT)",
				"CREATE TABLE test2 (id INT)",
			},
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Success.Code())
		t.Assert(res.Message, consts.Success.Message())
		t.Assert(res.TotalCount, 2)
		t.Assert(res.SuccessCount, 2)
		t.Assert(res.FailCount, 0)
		t.Assert(len(res.Errors), 0)
	})
}

// 测试 BatchExecuteSQL 方法 - 部分 SQL 执行失败
func Test_BatchExecuteSQL_PartialFailure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.BatchExecuteSQLFunc = func(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
			t.Assert(in.Schema, "test_schema")
			t.Assert(in.SQLList, []string{
				"CREATE TABLE test1 (id INT)",
				"INVALID SQL",
			})
			return &model.BatchExecuteSQLOutput{
				Success:      false,
				TotalCount:   2,
				SuccessCount: 1,
				FailCount:    1,
				Errors:       []string{"SQL syntax error"},
			}, nil
		}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema: "test_schema",
			SQLList: []string{
				"CREATE TABLE test1 (id INT)",
				"INVALID SQL",
			},
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
		t.Assert(res.TotalCount, 2)
		t.Assert(res.SuccessCount, 1)
		t.Assert(res.FailCount, 1)
		t.Assert(len(res.Errors), 1)
		t.Assert(res.Errors[0], "SQL syntax error")
	})
}

// 测试 BatchExecuteSQL 方法 - 所有 SQL 执行失败
func Test_BatchExecuteSQL_AllFailure(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.BatchExecuteSQLFunc = func(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
			t.Assert(in.Schema, "test_schema")
			t.Assert(in.SQLList, []string{
				"INVALID SQL 1",
				"INVALID SQL 2",
			})
			return &model.BatchExecuteSQLOutput{
				Success:      false,
				TotalCount:   2,
				SuccessCount: 0,
				FailCount:    2,
				Errors:       []string{"SQL syntax error 1", "SQL syntax error 2"},
			}, nil
		}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema: "test_schema",
			SQLList: []string{
				"INVALID SQL 1",
				"INVALID SQL 2",
			},
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
		t.Assert(res.TotalCount, 2)
		t.Assert(res.SuccessCount, 0)
		t.Assert(res.FailCount, 2)
		t.Assert(len(res.Errors), 2)
		t.Assert(res.Errors[0], "SQL syntax error 1")
		t.Assert(res.Errors[1], "SQL syntax error 2")
	})
}

// 测试 ExecuteSQL 方法 - 参数验证（Schema 为空）
func Test_ExecuteSQL_EmptySchema(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 执行测试
		req := &v1.ExecuteSQLReq{
			Schema: "", // 空 Schema
			RawSQL: "CREATE TABLE test (id INT)",
		}
		res, err := controller.ExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
	})
}

// 测试 ExecuteSQL 方法 - 参数验证（RawSQL 为空）
func Test_ExecuteSQL_EmptyRawSQL(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 执行测试
		req := &v1.ExecuteSQLReq{
			Schema: "test_schema",
			RawSQL: "", // 空 RawSQL
		}
		res, err := controller.ExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
	})
}

// 测试 BatchExecuteSQL 方法 - 参数验证（Schema 为空）
func Test_BatchExecuteSQL_EmptySchema(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema: "", // 空 Schema
			SQLList: []string{
				"CREATE TABLE test1 (id INT)",
				"CREATE TABLE test2 (id INT)",
			},
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
	})
}

// 测试 BatchExecuteSQL 方法 - 参数验证（SQLList 为空）
func Test_BatchExecuteSQL_EmptySQLList(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema:  "test_schema",
			SQLList: []string{}, // 空 SQLList
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
	})
}

// 测试 ExecuteSQL 方法 - 服务返回错误
func Test_ExecuteSQL_ServiceError(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.ExecuteSQLFunc = func(ctx context.Context, in model.ExecuteSQLInput) (out *model.ExecuteSQLOutput, err error) {
			return nil, errors.New("database connection error")
		}

		// 执行测试
		req := &v1.ExecuteSQLReq{
			Schema: "test_schema",
			RawSQL: "CREATE TABLE test (id INT)",
		}
		res, err := controller.ExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
		t.Assert(res.Message, "database connection error")
	})
}

// 测试 BatchExecuteSQL 方法 - 服务返回错误
func Test_BatchExecuteSQL_ServiceError(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建 Mock
		mockMariaDB := &MockMariaDB{}
		// 保存原始服务
		originalMariaDB := service.MariaDB()
		// 注册 Mock 服务
		service.RegisterMariaDB(mockMariaDB)
		// 测试完成后恢复原始服务
		defer service.RegisterMariaDB(originalMariaDB)

		// 创建 Mock 控制器
		controller := &MockControllerV1{}

		// 设置 Mock 行为
		mockMariaDB.BatchExecuteSQLFunc = func(ctx context.Context, in model.BatchExecuteSQLInput) (out *model.BatchExecuteSQLOutput, err error) {
			return nil, errors.New("database connection error")
		}

		// 执行测试
		req := &v1.BatchExecuteSQLReq{
			Schema: "test_schema",
			SQLList: []string{
				"CREATE TABLE test1 (id INT)",
				"CREATE TABLE test2 (id INT)",
			},
		}
		res, err := controller.BatchExecuteSQL(context.Background(), req)

		// 验证结果
		t.Assert(err, nil)
		t.Assert(res.Code, consts.Failed.Code())
		t.Assert(res.Message, "database connection error")
	})
}
