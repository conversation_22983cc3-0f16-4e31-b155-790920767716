package executeapi

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/model"
	"dataSyncHub/internal/service"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"dataSyncHub/api/executeapi/v1"
)

func (c *ControllerV1) BatchExecuteSQL(ctx context.Context, req *v1.BatchExecuteSQLReq) (res *v1.BatchExecuteSQLRes, err error) {
	res = &v1.BatchExecuteSQLRes{
		Code:    consts.Success.Code(),
		Message: consts.Success.Message(),
	}
	var out *model.BatchExecuteSQLOutput
	cost := gtime.FuncCost(func() {
		out, err = service.MariaDB().BatchExecuteSQL(
			ctx,
			model.BatchExecuteSQLInput{
				Schema:  req.Schema,
				SQLList: req.SQLList,
			},
		)
	})

	if err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()

	} else {
		res.TotalCount = out.TotalCount
		res.Errors = out.Errors
		res.FailCount = out.FailCount
		res.SuccessCount = out.SuccessCount
		if !out.Success {
			res.Code = consts.Failed.Code()
		}
	}
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
