package omnichannel

import (
	v1 "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/llms/gemini"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/internal/model/omnichannel"
	"brainHub/internal/service"
	"brainHub/utility"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/text/gstr"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
)

// answerHelper 回答創建輔助器
type answerHelper struct {
	ctx context.Context
	res *v1.ChatRes
}

// Chat 處理全管道聊天請求
func (c *ControllerV1) Chat(ctx context.Context, req *v1.ChatReq) (res *v1.ChatRes, err error) {
	// 初始化響應
	res = c.initializeChatResponse()

	// 創建回答輔助器
	helper := c.createAnswerHelper(ctx, res)

	// 聲明 AI 和響應變量，按需創建
	var ai llms.ILLMs
	var resp *llm.ResponseData

	// 處理聊天邏輯並記錄執行時間
	cost := gtime.FuncCost(func() {
		// 檢查是否為特殊密語
		handled, response := c.handleSpecialCommand(ctx, req, helper)
		if handled {
			// 特殊密語處理完成，創建響應數據
			resp = c.createResponseData(req, response)
			return
		}

		// 只有在需要正常聊天時才選擇 AI
		var selectErr error
		ai, resp, selectErr = c.selectAIAndCreateResponse(ctx, req, helper)
		if selectErr != nil {
			// AI 選擇失敗
			err = selectErr
			g.Log().Errorf(ctx, "Failed to select AI for normal chat: %v", selectErr)
			return
		}

		// 處理正常聊天
		chatResp, chatErr := c.processNormalChat(ctx, req, ai)
		if chatErr != nil {
			err = chatErr
			resp.Response = helper.createFailedAnswer(consts.OmniChannelChatFailed)
		} else {
			resp = chatResp
		}
	})

	// 記錄執行時間
	g.Log().Noticef(ctx, "Chat cost: %v", cost.String())

	// 如果在聊天處理過程中發生錯誤，確保有響應數據
	if err != nil && resp == nil {
		resp = c.createResponseData(req, helper.createFailedAnswer(consts.OmniChannelChatFailed))
	}

	// 處理 AI 響應（只有在沒有錯誤且響應需要處理時）
	if err == nil && resp != nil {
		err = c.processAIResponse(ctx, resp, helper)
	}

	// 最終確保響應數據完整性
	if resp == nil {
		resp = c.createResponseData(req, helper.createFailedAnswer(consts.OmniChannelChatFailed))
	}

	// 保存訊息
	userMessage := utility.ConvertAnyToGenericMessage(req)
	aiMessage := utility.ConvertAnyToGenericMessage(resp)
	c.saveMessages(ctx, userMessage, aiMessage)

	// ✅ 異步向量化處理
	g.Go(ctx, func(ctx context.Context) {
		if err := c.vectorizeMessages(ctx, userMessage, aiMessage); err != nil {
			g.Log().Errorf(ctx, "Vectorization failed: %v", err)
		}
	}, nil)

	// 返回響應
	g.RequestFromCtx(ctx).Response.WriteJsonExit(res)
	return
}

// initializeChatResponse 初始化聊天響應結構
func (c *ControllerV1) initializeChatResponse() *v1.ChatRes {
	return &v1.ChatRes{
		BaseRes: v1.BaseRes{
			Code:    consts.Success.Code(),
			Message: consts.Success.Message(),
		},
		Hit:       true,
		Sensitive: false,
	}
}

// createAnswerHelper 創建回答輔助器
func (c *ControllerV1) createAnswerHelper(ctx context.Context, res *v1.ChatRes) *answerHelper {
	return &answerHelper{
		ctx: ctx,
		res: res,
	}
}

// createFailedAnswer 創建失敗回答
func (ah *answerHelper) createFailedAnswer(code gcode.Code) string {
	ans, err := service.OmniChannel().CreateAnswer(ah.ctx, g.MapStrStr{
		code.Message(): "",
	})

	if err != nil {
		g.Log().Errorf(ah.ctx, "Failed to create failed answer: %v", err)
		return ""
	}

	if len(ans) > 0 {
		ah.res.Answer = gjson.New(ans).MustToJsonString()
		return ah.res.Answer
	}

	return ""
}

// createTextAnswer 創建文字回答
func (ah *answerHelper) createTextAnswer(text string) string {
	ans, err := service.OmniChannel().CreateAnswer(ah.ctx, g.MapStrStr{
		text: "",
	})

	if err != nil {
		g.Log().Errorf(ah.ctx, "Failed to create text answer: %v", err)
		return ""
	}

	if len(ans) > 0 {
		ah.res.Answer = gjson.New(ans).MustToJsonString()
		return ah.res.Answer
	}

	return ""
}

// createResponseData 創建響應數據結構
func (c *ControllerV1) createResponseData(req *v1.ChatReq, response string) *llm.ResponseData {
	return &llm.ResponseData{
		TenantID:  req.TenantID,
		UserID:    req.UserId,
		ServiceID: req.ServiceId,
		Channel:   req.Channel,
		Response:  response,
	}
}

// selectAIAndCreateResponse 選擇 AI 並創建響應數據
func (c *ControllerV1) selectAIAndCreateResponse(ctx context.Context, req *v1.ChatReq, helper *answerHelper) (llms.ILLMs, *llm.ResponseData, error) {
	// 創建基礎響應數據
	resp := c.createResponseData(req, consts.StringEmpty)

	// 選擇 AI
	ai, err := service.AiRouter().Select(ctx, &model.AiSelectorInput{
		TenantID:  req.TenantID,
		ServiceID: req.ServiceId,
		UserID:    req.UserId,
		Channel:   req.Channel,
	})

	if err != nil {
		g.Log().Errorf(ctx, "Failed to select AI: %v", err)

		// 根據錯誤類型創建相應的失敗回答
		if gerror.Code(err) == consts.CrawlWebsiteIsNotFinished {
			resp.Response = helper.createFailedAnswer(consts.CrawlWebsiteIsNotFinished)
		} else {
			resp.Response = helper.createFailedAnswer(consts.OmniChannelChatFailed)
		}

		return nil, resp, err
	}

	return ai, resp, nil
}

// handleSpecialCommand 處理特殊密語命令
func (c *ControllerV1) handleSpecialCommand(ctx context.Context, req *v1.ChatReq, helper *answerHelper) (handled bool, response string) {
	// 獲取特殊密語配置
	configKey := "system.encryption_setting."
	vPrefix, _ := g.Cfg().Get(ctx, configKey+"prefix")

	// 檢查是否匹配特殊密語前綴
	if vPrefix.IsEmpty() || !gstr.HasPrefix(req.Question, vPrefix.String()) {
		return false, consts.StringEmpty
	}

	g.Log().Infof(ctx, "Special command detected with prefix: %s", vPrefix.String())

	// 獲取必要的配置項
	vSettingUrl, _ := g.Cfg().Get(ctx, configKey+"setting_url")
	vSystemInstructionUrl, _ := g.Cfg().Get(ctx, configKey+"system_instruction")

	if vSettingUrl.IsEmpty() || vSystemInstructionUrl.IsEmpty() {
		g.Log().Warning(ctx, "Missing setting_url or system_instruction in config")
		return false, consts.StringEmpty
	}

	// 獲取模板配置
	vTemplate, _ := g.Cfg().Get(ctx, configKey+"template")
	if vTemplate.IsEmpty() {
		g.Log().Warning(ctx, "No template found in config file")
		return false, consts.StringEmpty
	}

	// 驗證模板格式並創建 Carousel
	if !gjson.Valid(vTemplate.String()) {
		g.Log().Warning(ctx, "Template is not valid JSON format")
		return false, consts.StringEmpty
	}

	carouselResponse, err := c.createCarouselFromTemplate(ctx, req, gjson.New(vTemplate.String()), vSettingUrl.String(), vSystemInstructionUrl.String())
	if err != nil {
		g.Log().Errorf(ctx, "Failed to create carousel from template: %v", err)
		return false, ""
	}

	helper.res.Answer = carouselResponse
	return true, carouselResponse
}

// createCarouselFromTemplate 從模板創建 Carousel 回答
func (c *ControllerV1) createCarouselFromTemplate(ctx context.Context, req *v1.ChatReq, template *gjson.Json, settingUrl, systemInstructionUrl string) (string, error) {
	// 構建 Carousel 輸入結構
	carouselInput := &omnichannel.CarouselInput{
		Text: template.Get("text").String(),
		Elements: []*omnichannel.CarouselElement{
			{
				Title:    template.Get("title").String(),
				Subtitle: template.Get("subTitle").String(),
				ImageUrl: template.Get("imageUrl").String(),
				Actions: []*omnichannel.CarouselAction{
					{
						Type:  "Url",
						Title: template.Get("title_setting").String(),
						Url:   fmt.Sprintf("%s?TenantID=%s&ServiceID=%s&UserID=%s", settingUrl, req.TenantID, req.ServiceId, req.UserId),
					},
					{
						Type:  "Url",
						Title: template.Get("title_system_instruction").String(),
						Url:   fmt.Sprintf("%s?TenantID=%s", systemInstructionUrl, req.TenantID),
					},
				},
			},
		},
	}

	// 創建 Carousel 回答
	ans, err := service.OmniChannel().CreateAnswer(ctx, carouselInput)
	if err != nil {
		return "", gerror.Wrapf(err, "Failed to create carousel answer")
	}

	if len(ans) == 0 {
		return "", gerror.New("Empty carousel answer created")
	}

	return gjson.New(ans).MustToJsonString(), nil
}

// processNormalChat 處理正常聊天邏輯，根據 MessageType 進行分支處理
func (c *ControllerV1) processNormalChat(ctx context.Context, req *v1.ChatReq, ai llms.ILLMs) (*llm.ResponseData, error) {
	if ai == nil {
		return nil, gerror.New("AI instance is nil")
	}

	// 根據 MessageType 進行分支處理
	messageType := gstr.ToLower(req.MessageType)
	if g.IsEmpty(messageType) {
		// 如果沒有指定 MessageType，預設為 text
		messageType = consts.MesssageTypeText
		g.Log().Debugf(ctx, "MessageType not specified, defaulting to text")
	}

	g.Log().Infof(ctx, "Processing chat with MessageType: %s", messageType)

	var message *llm.Message
	var tempFilePath string // 用於追蹤需要清理的臨時文件

	// 根據 MessageType 處理不同類型的內容
	switch messageType {
	case consts.MesssageTypeText, consts.MessageTypePostback:
		// text , post back 類型：按現有邏輯處理
		message = &llm.Message{
			Content:     req.Question,
			ContentType: consts.ContentTypeText,
		}
		g.Log().Debug(ctx, "Processing text message")

	case consts.MessageTypeImage:
		// image 類型：先下載文件，然後作為媒體文件處理
		localPath, mimeType, err := c.processMediaFile(ctx, req.Question, messageType)
		if err != nil {
			return nil, gerror.Wrapf(err, "failed to process image file")
		}
		tempFilePath = localPath // 記錄臨時文件路徑用於清理

		message = &llm.Message{
			Content:     localPath,
			ContentType: consts.ContentMediaFile,
			MimeType:    mimeType,
		}
		g.Log().Debugf(ctx, "Processing image message: %s (MIME: %s)", localPath, mimeType)

	case consts.MessageTypeVoice:
		// voice 類型：需要特殊處理
		processedContent, err := c.processVoiceMessage(ctx, req, ai)
		if err != nil {
			return nil, gerror.Wrapf(err, "failed to process voice message")
		}

		// 如果處理後的內容包含臨時文件路徑，記錄用於清理
		if processedContent.TempFilePath != consts.StringEmpty {
			tempFilePath = processedContent.TempFilePath
		}

		message = &llm.Message{
			Content:     processedContent.Content,
			ContentType: processedContent.ContentType,
			MimeType:    processedContent.MimeType,
		}
		g.Log().Debugf(ctx, "Processing voice message: content_type=%s", processedContent.ContentType)

	default:
		return nil, gerror.Newf("unsupported message type: %s", messageType)
	}

	// 確保在函數結束時清理臨時文件
	if tempFilePath != consts.StringEmpty {
		defer func() {
			if err := gfile.Remove(tempFilePath); err != nil {
				g.Log().Warningf(ctx, "Failed to clean up temporary file %s: %v", tempFilePath, err)
			} else {
				g.Log().Debugf(ctx, "Successfully cleaned up temporary file: %s", tempFilePath)
			}
		}()
	}

	// 調用 AI 進行聊天
	resp, err := ai.Chat(ctx, message)
	if err != nil {
		g.Log().Errorf(ctx, "AI chat failed: %v", err)
		return nil, err
	}

	if resp == nil {
		g.Log().Warning(ctx, "AI returned nil response")
		return nil, gerror.New("AI returned nil response")
	}

	// 確保響應數據完整性
	resp.TenantID = req.TenantID
	resp.UserID = req.UserId
	resp.ServiceID = req.ServiceId
	resp.Channel = req.Channel

	return resp, nil
}

// processAIResponse 處理 AI 響應數據
func (c *ControllerV1) processAIResponse(ctx context.Context, resp *llm.ResponseData, helper *answerHelper) error {
	if resp == nil {
		return gerror.New("Response data is nil")
	}

	// 解析 AI 響應格式
	strResult := utility.ParseJsonFormatResponse(gconv.String(resp.Response))
	resp.Response = strResult

	// 驗證響應格式
	if !gjson.Valid(strResult) {
		g.Log().Noticef(ctx, "Invalid JSON format response: %v", strResult)
		// 將無效 JSON 轉換為文字回答
		resp.Response = helper.createTextAnswer(strResult)
	} else {
		// 直接使用有效的 JSON 響應
		helper.res.Answer = strResult
	}

	return nil
}

// saveMessages 保存用戶和 AI 訊息到數據庫
func (c *ControllerV1) saveMessages(ctx context.Context, userMessage, aiMessage *model.GenericMessage) {
	// 保存用戶訊息
	if userMessage != nil {
		if err := service.DSH().InsertNewChatMessage(ctx, userMessage); err != nil {
			g.Log().Errorf(ctx, "Failed to save user message: %v", err)
		}
	}

	// 保存 AI 訊息
	if aiMessage != nil {
		if err := service.DSH().InsertNewChatMessage(ctx, aiMessage); err != nil {
			g.Log().Errorf(ctx, "Failed to save AI message: %v", err)
		}
	}
}

// ProcessedContent 處理後的內容結構
type ProcessedContent struct {
	Content      interface{} // 處理後的內容
	ContentType  string      // 內容類型
	MimeType     string      // MIME 類型
	TempFilePath string      // 臨時文件路徑（用於清理）
}

// processMediaFile 處理媒體文件（image 類型）
// 下載文件到臨時目錄並返回本地路徑和 MIME 類型
func (c *ControllerV1) processMediaFile(ctx context.Context, fileURL, messageType string) (localPath, mimeType string, err error) {
	g.Log().Infof(ctx, "Processing media file: %s (type: %s)", fileURL, messageType)

	// 使用工具函數下載文件
	localPath, mimeType, err = utility.DownloadFileFromURL(ctx, fileURL, messageType)
	if err != nil {
		err = gerror.Wrapf(err, "failed to download media file from URL: %s", fileURL)
		g.Log().Error(ctx, "processMediaFile download failed:", err)
		return "", "", err
	}

	g.Log().Infof(ctx, "Successfully processed media file: %s -> %s (MIME: %s)",
		fileURL, localPath, mimeType)

	return localPath, mimeType, nil
}

// processVoiceMessage 處理語音消息
// 根據當前 AI 類型決定是否需要先轉換為文本
func (c *ControllerV1) processVoiceMessage(ctx context.Context, req *v1.ChatReq, ai llms.ILLMs) (*ProcessedContent, error) {
	g.Log().Infof(ctx, "Processing voice message: %s", req.Question)

	// 先下載音頻文件
	localPath, mimeType, err := c.processMediaFile(ctx, req.Question, consts.MessageTypeVoice)
	if err != nil {
		return nil, gerror.Wrapf(err, "failed to download voice file")
	}

	// 檢查當前 AI 是否支持音頻處理
	// 如果是 Gemini，可以直接處理音頻；如果是其他模型，需要先轉文本
	if c.isGeminiAI(ai) {
		// Gemini 可以直接處理音頻文件
		g.Log().Debug(ctx, "Using Gemini to process audio directly")
		return &ProcessedContent{
			Content:      localPath,
			ContentType:  consts.ContentMediaFile,
			MimeType:     mimeType,
			TempFilePath: localPath,
		}, nil
	} else {
		// 其他模型需要先將音頻轉換為文本
		g.Log().Debug(ctx, "Converting audio to text for non-Gemini AI")
		text, err := c.convertAudioToTextWithDefaultGemini(ctx, localPath, mimeType)
		if err != nil {
			// 清理臨時文件
			_ = gfile.RemoveFile(localPath)
			return nil, gerror.Wrapf(err, "failed to convert audio to text")
		}

		return &ProcessedContent{
			Content:      text,
			ContentType:  consts.ContentTypeText,
			MimeType:     "",
			TempFilePath: localPath, // 仍需要清理原始音頻文件
		}, nil
	}
}

// isGeminiAI 檢查當前 AI 是否為 Gemini
func (c *ControllerV1) isGeminiAI(ai llms.ILLMs) bool {
	// 使用類型斷言檢查是否為 Gemini 實例
	_, isGemini := ai.(*gemini.GeminiLLM)
	return isGemini
}

// convertAudioToTextWithDefaultGemini 使用 default Gemini 將音頻轉換為文本
func (c *ControllerV1) convertAudioToTextWithDefaultGemini(ctx context.Context, audioFilePath, mimeType string) (text string, err error) {
	g.Log().Infof(ctx, "Converting audio to text using default Gemini: %s", audioFilePath)

	// 獲取 default llm 配置
	var vDefaultLLMs *g.Var
	vDefaultLLMs, err = g.Cfg().Get(ctx, consts.ConfigDefaultLLMs)
	if err != nil {
		err = gerror.Wrapf(err, "failed to get default_llms config")
		g.Log().Error(ctx, "convertAudioToTextWithDefaultGemini config retrieval failed:", err)
		return "", err
	}

	var defaultLLMsConfig *llm.LLMsConfig
	err = vDefaultLLMs.Struct(&defaultLLMsConfig)
	if err != nil {
		err = gerror.Wrapf(err, "failed to parse default_llms config")
		g.Log().Error(ctx, "convertAudioToTextWithDefaultGemini config parsing failed:", err)
		return "", err
	}

	// 創建 default Gemini 實例
	geminiLLM := gemini.New()

	// 創建包含音頻文件的 payload
	payload := &llm.Payload{
		Attachments: &model.Asset{
			Files: []string{audioFilePath},
		},
	}

	// 初始化 Gemini 實例
	err = geminiLLM.Initialize(ctx, defaultLLMsConfig, payload)
	if err != nil {
		err = gerror.Wrapf(err, "failed to initialize default Gemini instance")
		g.Log().Error(ctx, "convertAudioToTextWithDefaultGemini initialization failed:", err)
		return "", err
	}

	// 獲取音頻轉文本的 prompt 配置
	var vAudioPrompt *g.Var
	vAudioPrompt, err = g.Cfg().Get(ctx, consts.ConfigPromptsAudioToText)
	if err != nil {
		g.Log().Warning(ctx, "Failed to get audio_to_text_prompt config, using default")
		vAudioPrompt = g.NewVar(consts.DefaultAudioToTextPrompt)
	}

	audioPrompt := vAudioPrompt.String()
	if g.IsEmpty(audioPrompt) {
		audioPrompt = consts.DefaultAudioToTextPrompt
	}

	// 創建 GenerateContent 請求
	request := &llm.GenerateContentRequest{
		Prompt:           audioPrompt,
		MaxContinuations: consts.CacheExistsTrue,
		TotalTokenBudget: consts.DefaultAudioTokenBudget,
		IncludeThinking:  false,
	}

	// 使用 GenerateContent 接口進行音頻轉文本
	response, err := geminiLLM.GenerateContent(ctx, request)
	if err != nil {
		err = gerror.Wrapf(err, "failed to convert audio to text using Gemini GenerateContent")
		g.Log().Error(ctx, "convertAudioToTextWithDefaultGemini GenerateContent failed:", err)
		return "", err
	}

	if response == nil || g.IsEmpty(response.OutputContent) {
		err = gerror.New("audio to text conversion returned empty result")
		g.Log().Error(ctx, "convertAudioToTextWithDefaultGemini empty result:", err)
		return "", err
	}

	text = response.OutputContent
	g.Log().Infof(ctx, "Successfully converted audio to text: %d characters", len(text))

	// 記錄轉換統計信息
	g.Log().Debugf(ctx, "Audio conversion stats - input_tokens: %d, output_tokens: %d, generation_time: %dms",
		response.InputTokens, response.OutputTokens, response.GenerationTime)

	return text, nil
}

// vectorizeMessages 向量化用戶和 AI 訊息
func (c *ControllerV1) vectorizeMessages(ctx context.Context, userMessage, aiMessage *model.GenericMessage) error {
	now := time.Now().Format(time.RFC3339)
	var rows []map[string]any

	// 處理用戶訊息
	if userMessage != nil {
		rows = append(rows, map[string]any{
			"tenant":     userMessage.TenantID,
			"collection": consts.ChatMessage,
			"properties": g.Map{
				consts.PropMessage:     userMessage.Message,
				consts.PropServiceID:   userMessage.ServiceID,
				consts.PropUserID:      userMessage.UserID,
				consts.PropRole:        userMessage.Role,
				consts.PropChannel:     userMessage.Channel,
				consts.PropMessageType: userMessage.MessageType,
				consts.PropDisplayName: userMessage.DisplayName,
				consts.PropCrateAt:     now, // ✅ 使用 RFC3339 格式
			},
		})
	}

	// 處理 AI 訊息
	if aiMessage != nil {
		// 解析 AI 回覆的 JSON 結構，提取第一個元素的 text 屬性
		extractedText := c.extractAIMessageText(aiMessage.Message)
		rows = append(rows, map[string]any{
			"tenant":     aiMessage.TenantID,
			"collection": consts.ChatMessage,
			"properties": g.Map{
				consts.PropMessage:     extractedText,
				consts.PropServiceID:   aiMessage.ServiceID,
				consts.PropUserID:      aiMessage.UserID,
				consts.PropRole:        aiMessage.Role,
				consts.PropChannel:     aiMessage.Channel,
				consts.PropMessageType: aiMessage.MessageType,
				consts.PropDisplayName: aiMessage.DisplayName,
				consts.PropCrateAt:     now, // ✅ 使用 RFC3339 格式
			},
		})
	}

	// ✅ 通過 MessageQ 發送向量化消息
	payload := g.Map{"data": rows}
	err := service.MessageQ().Send(ctx,
		consts.RouteKeyWeaviateBrainHub,
		consts.ActionCreateData,
		[]byte(gjson.New(payload).MustToJsonString()),
	)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to send vectorization message: %v", err)
		return err
	}

	g.Log().Infof(ctx, "Vectorization message sent successfully, data count: %d", len(rows))
	return nil
}

// extractAIMessageText 解析 AI 回覆的 JSON 結構
func (c *ControllerV1) extractAIMessageText(message interface{}) string {
	// 解析 AI 回覆的 JSON 結構
	// 提取第一個元素的第一層級 text 屬性
	jsonStr := gconv.String(message)
	jsonData := gjson.New(jsonStr)

	// 檢查是否為數組且有元素
	arrayData := jsonData.Array()
	if len(arrayData) > 0 {
		firstElement := gjson.New(arrayData[0])
		if firstElement != nil {
			if text := firstElement.Get("text"); !text.IsNil() {
				return text.String()
			}
		}
	}

	// 降級處理：如果解析失敗，返回原始字符串
	return jsonStr
}
