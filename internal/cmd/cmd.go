package cmd

import (
	"context"
	"dataSyncHub/internal/consts"
	"dataSyncHub/internal/controller/executeapi"
	"dataSyncHub/internal/controller/queryapi"
	"dataSyncHub/internal/controller/vector"
	"dataSyncHub/internal/service"
	"dataSyncHub/utility"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	"net/http"
)

func init() {
}

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			// 在 Nacos 配置加載後設置日誌標誌
			g.Log().SetFlags(glog.F_FILE_SHORT | glog.F_TIME_STD | glog.F_CALLER_FN)

			service.MessageQ().RegisterHandler(consts.RouteKeyMariadbPrefix, service.MariaDB().OnMessage)
			service.MessageQ().RegisterHandler(consts.RouteKeyWeaviatePrefix, service.VecWeaviate().OnMessage)

			err = service.MessageQ().InitReceive(ctx)

			if err != nil {
				g.Log().Error(ctx, err)
				panic(err)
			}
			defer service.MessageQ().Close()

			s := g.Server(consts.ServiceName)
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(MiddleHandler, ghttp.MiddlewareHandlerResponse)
				group.Bind(
					vector.NewV1(),
					queryapi.NewV1(),
					executeapi.NewV1(),
				)
			})
			s.Run()
			return nil
		},
	}
)

func MiddleHandler(r *ghttp.Request) {
	ctx := r.GetCtx()
	r.Response.CORSDefault()
	logger := g.Log().Cat(consts.CatHttpLogs)
	vServices, _ := g.Cfg().Get(ctx, "system.white_list")
	if !vServices.IsEmpty() {
		services := gstr.SplitAndTrim(vServices.String(), ",")
		serviceName := r.GetHeader(consts.XHeaderService)
		if !garray.NewStrArrayFrom(services).Contains(serviceName) {
			r.Response.WriteStatusExit(http.StatusForbidden)
		}
	}

	// Log request
	requestBody := r.GetBodyString()

	if gjson.Valid(requestBody) {

		jsReq := gjson.New(requestBody)
		reqData := jsReq.MustToJsonIndentString()

		newReq, e := utility.ProcessVectorJSON(reqData)
		if e == nil {
			reqData = newReq
		}

		logger.Debugf(ctx, "Request-Uri: %s Body: %s", r.RequestURI, reqData)
	} else {
		logger.Debugf(ctx, "Request-Uri: %s", r.RequestURI)
	}

	r.Middleware.Next()

	// Log response
	responseBody := r.Response.BufferString()
	if gjson.Valid(responseBody) {
		jsRes := gjson.New(responseBody)
		strRes := jsRes.MustToJsonIndentString()
		newRes, e := utility.ProcessVectorJSON(strRes)
		if e == nil {
			strRes = newRes
		}

		logger.Debugf(ctx, "Response: %s", strRes)
	} else {
		logger.Debugf(ctx, "Return response (no valid JSON) ,status=%d", r.Response.Status)
	}
}
