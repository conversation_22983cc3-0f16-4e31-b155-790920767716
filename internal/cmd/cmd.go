package cmd

import (
	"brainHub/internal/consts"
	"brainHub/internal/controller/brain"
	"brainHub/internal/controller/embedding"
	"brainHub/internal/controller/omnichannel"
	"brainHub/internal/logic/messageQ"
	"brainHub/internal/model"
	"brainHub/internal/service"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/gtimer"
)

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {

			if err := messageQ.SetupMessageQ(ctx); err != nil {
				g.Log().Error(ctx, err)
				panic(err)
			}

			// ✅ 初始化清理服務
			if err := initCleanupServices(ctx); err != nil {
				g.Log().Error(ctx, err)
				panic(err)
			}

			s := g.Server(consts.ServiceName)
			s.Group("/", func(group *ghttp.RouterGroup) {
				group.Middleware(MiddleHandler, ghttp.MiddlewareHandlerResponse)
				group.Bind(
					omnichannel.NewV1(),
					brain.NewV1(),
					embedding.NewV1(),
				)
			})
			s.Run()
			return nil
		},
	}
)

// initCleanupServices 初始化清理服務
func initCleanupServices(ctx context.Context) error {
	g.Log().Info(ctx, "Initializing cleanup services")

	// 創建並啟動聊天消息清理服務
	cleanupService := NewChatMessageCleanupService()
	err := cleanupService.Start(ctx)
	if err != nil {
		return gerror.Wrapf(err, "failed to start chat message cleanup service")
	}

	g.Log().Info(ctx, "Cleanup services initialized successfully")
	return nil
}

// ChatMessageCleanupService 聊天消息清理服務
type ChatMessageCleanupService struct {
	timer interface{} // gtimer.Timer 的接口
}

// NewChatMessageCleanupService 創建清理服務實例
func NewChatMessageCleanupService() *ChatMessageCleanupService {
	return &ChatMessageCleanupService{}
}

// Start 啟動清理服務
func (s *ChatMessageCleanupService) Start(ctx context.Context) error {
	g.Log().Info(ctx, "Starting chat message cleanup service")

	// 計算到下一個零點的時間間隔
	now := time.Now()
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
	initialDelay := nextMidnight.Sub(now)

	g.Log().Infof(ctx, "Next cleanup will run at: %s (in %v)", nextMidnight.Format(time.RFC3339), initialDelay)

	// 設置初始延遲，確保第一次在零點執行
	gtimer.SetTimeout(ctx, initialDelay, func(ctx context.Context) {
		s.performCleanup(ctx)
		// 設置定時器，每24小時執行一次
		gtimer.SetInterval(ctx, 24*time.Hour, func(ctx context.Context) {
			s.performCleanup(ctx)
		})
	})

	return nil
}

// performCleanup 執行清理操作
func (s *ChatMessageCleanupService) performCleanup(ctx context.Context) {
	g.Log().Info(ctx, "Starting chat message cleanup task")

	// 從配置讀取保留天數
	vKeepDays, err := g.Cfg().Get(ctx, "system.chat_message.keep_history_days", 90)
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get keep_history_days config: %v", err)
		return
	}

	keepDays := vKeepDays.Int()
	cutoff := time.Now().AddDate(0, 0, -keepDays)

	g.Log().Infof(ctx, "Cleaning chat messages older than %d days (before %s)", keepDays, cutoff.Format(time.RFC3339))

	// 獲取所有租戶
	tenants, err := service.DSH().GetTenantAndCollections(ctx, []string{consts.ChatMessage})
	if err != nil {
		g.Log().Errorf(ctx, "Failed to get tenants: %v", err)
		return
	}

	g.Log().Infof(ctx, "Found %d tenants to process", len(tenants))

	// 遍歷所有租戶進行清理
	for _, tenant := range tenants {
		s.cleanupTenantData(ctx, tenant, cutoff)
	}

	g.Log().Info(ctx, "Chat message cleanup task completed")
}

// cleanupTenantData 清理指定租戶的數據
func (s *ChatMessageCleanupService) cleanupTenantData(ctx context.Context, tenant string, cutoff time.Time) {
	g.Log().Infof(ctx, "Cleaning data for tenant: %s", tenant)

	// 1. 清理 Weaviate 中的向量數據
	s.cleanupWeaviateData(ctx, tenant, cutoff)

	// 2. 清理 MariaDB 中的聊天記錄
	s.cleanupMariaDBData(ctx, tenant, cutoff)
}

// cleanupWeaviateData 清理 Weaviate 中的向量數據
func (s *ChatMessageCleanupService) cleanupWeaviateData(ctx context.Context, tenant string, cutoff time.Time) {
	// ✅ 創建過濾條件（簡化版本，避免依賴問題）
	filterStr := fmt.Sprintf(`{"where":{"operator":"LessThan","path":["%s"],"valueDate":"%s"}}`,
		consts.PropCrateAt, cutoff.Format(time.RFC3339))

	// 創建清理請求
	clearRequest := g.Map{
		"collection_name": consts.ChatMessage,
		"tenant":          tenant,
		"filter":          filterStr,
	}

	// 通過 MessageQ 發送清理消息
	data := gjson.New(clearRequest).MustToJsonString()
	err := service.MessageQ().Send(ctx,
		consts.RouteKeyWeaviateBrainHub,
		consts.ActionClearDataByFilter,
		[]byte(data),
	)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to send Weaviate cleanup message for tenant %s: %v", tenant, err)
	} else {
		g.Log().Infof(ctx, "Weaviate cleanup message sent for tenant: %s", tenant)
	}
}

// cleanupMariaDBData 清理 MariaDB 中的聊天記錄
func (s *ChatMessageCleanupService) cleanupMariaDBData(ctx context.Context, tenant string, cutoff time.Time) {
	// 獲取當前年月用於表名
	currentYearMonth := time.Now().Format("2006_01")
	tableName := fmt.Sprintf(consts.TableChatMessage, currentYearMonth)

	// 創建 MariaDB 清理消息
	mqMessage := &model.MQMessage{
		Schema:          tenant,
		Table:           tableName,
		WhereConditions: fmt.Sprintf("%s < ?", consts.DBFieldCreateAt),
		WhereParams:     []interface{}{cutoff.Format(time.RFC3339)},
	}

	// 通過 MessageQ 發送清理消息
	data := gjson.New(mqMessage).MustToJsonString()
	err := service.MessageQ().Send(ctx,
		consts.RouteKeyMariadbBrainHub,
		consts.ActionDelete,
		[]byte(data),
	)

	if err != nil {
		g.Log().Errorf(ctx, "Failed to send MariaDB cleanup message for tenant %s: %v", tenant, err)
	} else {
		g.Log().Infof(ctx, "MariaDB cleanup message sent for tenant: %s", tenant)
	}
}

func MiddleHandler(r *ghttp.Request) {
	// Interception and processing of all requests are processed in middleware
	ctx := r.GetCtx()
	r.Response.CORSDefault()
	if gjson.Valid(r.GetBodyString()) {
		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Request-Uri: %s Body:%s", r.RequestURI, r.GetBodyString())
	} else {
		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Request-Uri: %s ", r.RequestURI)

	}
	r.Middleware.Next()

	if gjson.Valid(r.Response.BufferString()) {

		g.Log().Cat(consts.CatalogReqRes).Debugf(ctx, "Response-%s", r.Response.BufferString())
	} else {
		g.Log().Cat(consts.CatalogReqRes).Debug(ctx, "Return response")

	}

}
