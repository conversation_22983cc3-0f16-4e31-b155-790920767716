package utility

import (
	"encoding/json"
	"strings"
)

// ProcessVectorJSON takes a JSON string as input, finds all keys named 'vector' (case-insensitive),
// replaces their values with '...', and returns the processed JSON string.
// It handles both simple and nested JSON structures.
func ProcessVectorJSON(jsonStr string) (string, error) {
	// Parse the JSON string
	var data interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	if err != nil {
		return "", err
	}

	// Process the data
	processedData := processData(data)

	// Convert back to JSON string
	result, err := json.Marshal(processedData)
	if err != nil {
		return "", err
	}

	return string(result), nil
}

// processData recursively processes the data structure
func processData(data interface{}) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		// Process each key-value pair in the map
		for key, value := range v {
			if strings.ToLower(key) == "vector" {
				// Replace the value with "..."
				v[key] = "..."
			} else {
				// Recursively process nested structures
				v[key] = processData(value)
			}
		}
		return v
	case []interface{}:
		// Process each element in the array
		for i, item := range v {
			v[i] = processData(item)
		}
		return v
	default:
		// Return primitive values as is
		return v
	}
}
