package utility

import (
	"encoding/json"
	"fmt"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// WhereCondition 表示一個 WHERE 條件的 JSON 結構
type WhereCondition struct {
	Operator          string           `json:"operator,omitempty"`
	Path              []string         `json:"path,omitempty"`
	ValueText         []string         `json:"valueText,omitempty"`
	ValueTextArray    []string         `json:"valueTextArray,omitempty"`
	ValueInt          *int64           `json:"valueInt,omitempty"`
	ValueIntArray     []int64          `json:"valueIntArray,omitempty"`
	ValueNumber       *float64         `json:"valueNumber,omitempty"`
	ValueNumberArray  []float64        `json:"valueNumberArray,omitempty"`
	ValueBoolean      *bool            `json:"valueBoolean,omitempty"`
	ValueBooleanArray []bool           `json:"valueBooleanArray,omitempty"`
	ValueDate         *string          `json:"valueDate,omitempty"`
	ValueGeoRange     *GeoRange        `json:"valueGeoRange,omitempty"`
	Operands          []WhereCondition `json:"operands,omitempty"`
}

// GeoRange 表示地理範圍查詢
type GeoRange struct {
	GeoCoordinates GeoCoordinates `json:"geoCoordinates"`
	Distance       Distance       `json:"distance"`
}

type GeoCoordinates struct {
	Latitude  float32 `json:"latitude"`
	Longitude float32 `json:"longitude"`
}

type Distance struct {
	Max float32 `json:"max"`
}

// JSONToWhereBuilder 將 JSON 字符串轉換為 filters.WhereBuilder
// 支持兩種格式：
// 1. 標準 JSON 格式：{"operator": "Equal", "path": ["name"], "valueText": ["value"]}
// 2. Where 字符串格式："where:{operator: ContainsAny path: [\"relId\"] valueText: [\"uuid1\",\"uuid2\"]}"
func JSONToWhereBuilder(jsonStr string) (*filters.WhereBuilder, error) {
	jsonStr = strings.TrimSpace(jsonStr)

	// 如果是引號包裹的字符串，先去除引號
	if strings.HasPrefix(jsonStr, `"`) && strings.HasSuffix(jsonStr, `"`) {
		jsonStr = strings.Trim(jsonStr, `"`)
	}

	// 檢查是否是 where: 格式
	if strings.HasPrefix(jsonStr, "where:") {
		return parseWhereString(jsonStr)
	}

	// 否則按標準 JSON 處理
	var condition WhereCondition
	if err := json.Unmarshal([]byte(jsonStr), &condition); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	return buildWhereFromCondition(&condition)
}

// buildWhereFromCondition 根據條件構建 WhereBuilder
func buildWhereFromCondition(condition *WhereCondition) (*filters.WhereBuilder, error) {
	// 處理邏輯操作符 (And, Or)
	if condition.Operator == "And" || condition.Operator == "Or" {
		if len(condition.Operands) == 0 {
			return nil, fmt.Errorf("%s 操作符需要至少一個操作數", condition.Operator)
		}

		// 構建子條件
		var operands []*filters.WhereBuilder
		for _, operand := range condition.Operands {
			subCondition, err := buildWhereFromCondition(&operand)
			if err != nil {
				return nil, err
			}
			operands = append(operands, subCondition)
		}

		// 組合條件
		if condition.Operator == "And" {
			return filters.Where().WithOperator(filters.And).WithOperands(operands), nil
		} else {
			return filters.Where().WithOperator(filters.Or).WithOperands(operands), nil
		}
	}

	// 構建基礎 WhereBuilder
	builder := filters.Where()

	// 設置路徑
	if len(condition.Path) > 0 {
		builder = builder.WithPath(condition.Path)
	}

	// 根據操作符類型設置值
	operator, err := parseOperator(condition.Operator)
	if err != nil {
		return nil, err
	}

	builder = builder.WithOperator(operator)

	// 根據不同的值類型設置相應的值
	if len(condition.ValueText) > 0 {
		if len(condition.ValueText) == 1 {
			builder = builder.WithValueText(condition.ValueText[0])
		} else {
			builder = builder.WithValueText(condition.ValueText...)
		}
	} else if len(condition.ValueTextArray) > 0 {
		builder = builder.WithValueText(condition.ValueTextArray...)
	} else if condition.ValueInt != nil {
		builder = builder.WithValueInt(*condition.ValueInt)
	} else if len(condition.ValueIntArray) > 0 {
		builder = builder.WithValueInt(condition.ValueIntArray...)
	} else if condition.ValueNumber != nil {
		builder = builder.WithValueNumber(*condition.ValueNumber)
	} else if len(condition.ValueNumberArray) > 0 {
		builder = builder.WithValueNumber(condition.ValueNumberArray...)
	} else if condition.ValueBoolean != nil {
		builder = builder.WithValueBoolean(*condition.ValueBoolean)
	} else if len(condition.ValueBooleanArray) > 0 {
		builder = builder.WithValueBoolean(condition.ValueBooleanArray...)
	} else if condition.ValueDate != nil {
		// 解析日期字符串
		t, err := time.Parse(time.RFC3339, *condition.ValueDate)
		if err != nil {
			return nil, fmt.Errorf("解析日期失敗: %w", err)
		}
		builder = builder.WithValueDate(t)
	} else if condition.ValueGeoRange != nil {
		builder = builder.WithValueGeoRange(&filters.GeoCoordinatesParameter{
			Latitude:    condition.ValueGeoRange.GeoCoordinates.Latitude,
			Longitude:   condition.ValueGeoRange.GeoCoordinates.Longitude,
			MaxDistance: condition.ValueGeoRange.Distance.Max,
		})
	}

	return builder, nil
}

// parseOperator 將字符串操作符轉換為 filters.WhereOperator
func parseOperator(op string) (filters.WhereOperator, error) {
	switch op {
	// 比較操作符
	case "Equal":
		return filters.Equal, nil
	case "NotEqual":
		return filters.NotEqual, nil
	case "GreaterThan":
		return filters.GreaterThan, nil
	case "GreaterThanEqual":
		return filters.GreaterThanEqual, nil
	case "LessThan":
		return filters.LessThan, nil
	case "LessThanEqual":
		return filters.LessThanEqual, nil

	// 文本操作符
	case "Like":
		return filters.Like, nil
	case "ContainsAny":
		return filters.ContainsAny, nil
	case "ContainsAll":
		return filters.ContainsAll, nil

	// 邏輯操作符
	case "And":
		return filters.And, nil
	case "Or":
		return filters.Or, nil
	case "Not":
		return filters.Not, nil

	// 地理操作符
	case "WithinGeoRange":
		return filters.WithinGeoRange, nil

	// 存在性操作符
	case "IsNull":
		return filters.IsNull, nil

	default:
		return "", fmt.Errorf("unsupported operator: %s", op)
	}
}

// parseWhereString 解析 "where:{operator: ... }" 格式的字符串
func parseWhereString(whereStr string) (*filters.WhereBuilder, error) {
	// 移除 "where:" 前綴
	whereStr = strings.TrimPrefix(whereStr, "where:")
	whereStr = strings.TrimSpace(whereStr)

	// 轉換為標準 JSON 格式
	jsonStr, err := convertWhereStringToJSON(whereStr)
	if err != nil {
		return nil, err
	}

	// 解析 JSON
	var condition WhereCondition
	if err := json.Unmarshal([]byte(jsonStr), &condition); err != nil {
		return nil, fmt.Errorf("failed to parse converted JSON: %w", err)
	}

	return buildWhereFromCondition(&condition)
}

// convertWhereStringToJSON 將 where 字符串轉換為標準 JSON
func convertWhereStringToJSON(whereStr string) (string, error) {
	return convertWhereStringToJSONV2(whereStr)
}

// convertWhereStringToJSONOld 將 where 字符串轉換為標準 JSON（舊版本）
func convertWhereStringToJSONOld(whereStr string) (string, error) {
	// 移除外層大括號
	whereStr = strings.TrimPrefix(whereStr, "{")
	whereStr = strings.TrimSuffix(whereStr, "}")
	whereStr = strings.TrimSpace(whereStr)

	result := make(map[string]interface{})

	// 首先找到 operands 的位置，以便在解析其他字段時排除這些區域
	operandsStart := -1
	operandsEnd := -1
	if idx := strings.Index(whereStr, "operands:"); idx != -1 {
		operandsStart = idx
		// 找到對應的結束位置
		bracketCount := 0
		inBracket := false
		for i := idx + len("operands:"); i < len(whereStr); i++ {
			if whereStr[i] == '[' && !inBracket {
				inBracket = true
				bracketCount = 1
			} else if inBracket {
				if whereStr[i] == '[' {
					bracketCount++
				} else if whereStr[i] == ']' {
					bracketCount--
					if bracketCount == 0 {
						operandsEnd = i + 1
						break
					}
				}
			}
		}
	}

	// 創建一個不包含 operands 內容的字符串用於解析其他字段
	searchStr := whereStr
	if operandsStart != -1 && operandsEnd != -1 {
		searchStr = whereStr[:operandsStart] + whereStr[operandsEnd:]
	}

	// 解析 operator
	operatorRegex := regexp.MustCompile(`operator:\s*(\w+)`)
	if match := operatorRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		result["operator"] = match[1]
	}

	// 解析 path - 使用非貪婪模式並處理轉義字符
	pathRegex := regexp.MustCompile(`path:\s*\[([^\]]*(?:\\.[^\]]*)*)\]`)
	if match := pathRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		paths := parseStringArray(match[1])
		result["path"] = paths
	}

	// 解析 valueText - 處理可能很長的值列表
	// 先找到 valueText: 的位置
	valueTextStart := strings.Index(searchStr, "valueText:")
	if valueTextStart != -1 {
		// 從 valueText: 開始找到對應的結束括號
		afterValueText := searchStr[valueTextStart+10:] // "valueText:" 長度為 10
		afterValueText = strings.TrimSpace(afterValueText)

		if strings.HasPrefix(afterValueText, "[") {
			// 找到匹配的結束括號
			bracketCount := 1
			endPos := 1
			inQuotes := false
			escape := false

			for i := 1; i < len(afterValueText); i++ {
				if escape {
					escape = false
					continue
				}

				if afterValueText[i] == '\\' {
					escape = true
					continue
				}

				if afterValueText[i] == '"' {
					inQuotes = !inQuotes
				}

				if !inQuotes {
					if afterValueText[i] == '[' {
						bracketCount++
					} else if afterValueText[i] == ']' {
						bracketCount--
						if bracketCount == 0 {
							endPos = i + 1
							break
						}
					}
				}
			}

			if endPos > 1 {
				valueTextContent := afterValueText[1 : endPos-1] // 去掉 [ 和 ]
				values := parseStringArray(valueTextContent)
				result["valueText"] = values
			}
		}
	}

	// 解析單個 valueText (不帶數組)
	if _, hasValueText := result["valueText"]; !hasValueText {
		singleValueRegex := regexp.MustCompile(`valueText:\s*"([^"]*)"`)
		if match := singleValueRegex.FindStringSubmatch(searchStr); len(match) > 1 {
			result["valueText"] = []string{match[1]}
		}
	}

	// 解析 valueInt
	valueIntRegex := regexp.MustCompile(`valueInt:\s*(-?\d+)`)
	if match := valueIntRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		// 將字符串轉換為整數
		if num, err := strconv.ParseInt(match[1], 10, 64); err == nil {
			result["valueInt"] = num
		}
	}

	// 解析 valueNumber
	valueNumberRegex := regexp.MustCompile(`valueNumber:\s*(-?[\d.]+)`)
	if match := valueNumberRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		// 將字符串轉換為浮點數
		if num, err := strconv.ParseFloat(match[1], 64); err == nil {
			result["valueNumber"] = num
		}
	}

	// 解析 valueBoolean
	valueBooleanRegex := regexp.MustCompile(`valueBoolean:\s*(true|false)`)
	if match := valueBooleanRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		result["valueBoolean"] = match[1] == "true"
	}

	// 解析 valueDate
	valueDateRegex := regexp.MustCompile(`valueDate:\s*"([^"]*)"`)
	if match := valueDateRegex.FindStringSubmatch(searchStr); len(match) > 1 {
		result["valueDate"] = match[1]
	}

	// 處理嵌套的 operands
	if operandsStart != -1 && operandsEnd != -1 {
		operands, err := parseOperands(whereStr)
		if err != nil {
			return "", err
		}
		if len(operands) > 0 {
			result["operands"] = operands
		}
	}

	// 轉換為 JSON
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("轉換為 JSON 失敗: %w", err)
	}

	return string(jsonBytes), nil
}

// parseStringArray 解析字符串數組
func parseStringArray(str string) []string {
	var result []string
	str = strings.TrimSpace(str)

	// 處理空字符串
	if str == "" {
		return result
	}

	// 檢查是否有轉義的引號 \"
	if strings.Contains(str, `\"`) {
		// 有轉義引號的情況
		// 例如: \"value1\",\"value2\" 應該解析為 ["value1", "value2"]

		// 分割逗號
		parts := strings.Split(str, ",")
		for _, part := range parts {
			part = strings.TrimSpace(part)
			// 移除轉義的引號
			if strings.HasPrefix(part, `\"`) && strings.HasSuffix(part, `\"`) {
				// 去掉前後的 \"
				part = part[2 : len(part)-2]
			}
			if part != "" {
				result = append(result, part)
			}
		}
	} else {
		// 沒有轉義引號，正常處理
		// 使用正則表達式找所有引號內的內容
		re := regexp.MustCompile(`"([^"]+)"`)
		matches := re.FindAllStringSubmatch(str, -1)

		if len(matches) > 0 {
			for _, match := range matches {
				if len(match) > 1 {
					result = append(result, match[1])
				}
			}
		} else {
			// 如果沒有引號，按逗號分割
			parts := strings.Split(str, ",")
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if part != "" {
					result = append(result, part)
				}
			}
		}
	}

	return result
}

// parseOperands 解析嵌套的 operands
func parseOperands(whereStr string) ([]map[string]interface{}, error) {
	var operands []map[string]interface{}

	// 找到 operands:[ 的位置
	start := strings.Index(whereStr, "operands:[")
	if start == -1 {
		return operands, nil
	}

	// 找到對應的結束括號
	bracketCount := 1
	startPos := start + len("operands:[")
	endPos := startPos

	for i := startPos; i < len(whereStr); i++ {
		if whereStr[i] == '[' {
			bracketCount++
		} else if whereStr[i] == ']' {
			bracketCount--
			if bracketCount == 0 {
				endPos = i
				break
			}
		}
	}

	if bracketCount != 0 {
		return nil, fmt.Errorf("operands 括號不匹配")
	}

	// 提取 operands 內容
	operandsStr := whereStr[startPos:endPos]

	// 解析每個 operand
	// 使用更精確的分割方法
	operandParts := splitOperands(operandsStr)

	for _, part := range operandParts {
		operand := make(map[string]interface{})

		// 解析 operator
		if match := regexp.MustCompile(`operator:\s*(\w+)`).FindStringSubmatch(part); len(match) > 1 {
			operand["operator"] = match[1]
		}

		// 解析 path
		if match := regexp.MustCompile(`path:\s*\[([^\]]*)\]`).FindStringSubmatch(part); len(match) > 1 {
			operand["path"] = parseStringArray(match[1])
		}

		// 解析 valueText 數組
		if match := regexp.MustCompile(`valueText:\s*\[([^\]]*)\]`).FindStringSubmatch(part); len(match) > 1 {
			operand["valueText"] = parseStringArray(match[1])
		} else if match := regexp.MustCompile(`valueText:\s*"([^"]*)"`).FindStringSubmatch(part); len(match) > 1 {
			operand["valueText"] = []string{match[1]}
		}

		// 解析 valueInt
		if match := regexp.MustCompile(`valueInt:\s*(-?\d+)`).FindStringSubmatch(part); len(match) > 1 {
			// 將字符串轉換為整數
			if num, err := strconv.ParseInt(match[1], 10, 64); err == nil {
				operand["valueInt"] = num
			}
		}

		// 解析 valueNumber
		if match := regexp.MustCompile(`valueNumber:\s*(-?[\d.]+)`).FindStringSubmatch(part); len(match) > 1 {
			// 將字符串轉換為浮點數
			if num, err := strconv.ParseFloat(match[1], 64); err == nil {
				operand["valueNumber"] = num
			}
		}

		// 解析 valueBoolean
		if match := regexp.MustCompile(`valueBoolean:\s*(true|false)`).FindStringSubmatch(part); len(match) > 1 {
			operand["valueBoolean"] = match[1] == "true"
		}

		// 處理嵌套的 operands
		if strings.Contains(part, "operands:") {
			subOperands, err := parseOperands(part)
			if err != nil {
				return nil, err
			}
			if len(subOperands) > 0 {
				operand["operands"] = subOperands
			}
		}

		if len(operand) > 0 {
			operands = append(operands, operand)
		}
	}

	return operands, nil
}

// splitOperands 智能分割 operands，處理嵌套情況
func splitOperands(operandsStr string) []string {
	var parts []string
	var currentPart strings.Builder
	bracketCount := 0
	inQuotes := false
	escape := false

	for i := 0; i < len(operandsStr); i++ {
		char := operandsStr[i]

		// 處理轉義字符
		if escape {
			currentPart.WriteByte(char)
			escape = false
			continue
		}

		if char == '\\' {
			escape = true
			currentPart.WriteByte(char)
			continue
		}

		// 處理引號
		if char == '"' && !escape {
			inQuotes = !inQuotes
		}

		// 只在不在引號內時處理括號
		if !inQuotes {
			if char == '{' {
				bracketCount++
			} else if char == '}' {
				bracketCount--
				currentPart.WriteByte(char)

				// 當回到頂層時，完成一個 operand
				if bracketCount == 0 {
					part := strings.TrimSpace(currentPart.String())
					if part != "" {
						parts = append(parts, part)
						currentPart.Reset()
					}
					// 跳過逗號和空格
					for i+1 < len(operandsStr) && (operandsStr[i+1] == ',' || operandsStr[i+1] == ' ') {
						i++
					}
					continue
				}
			}
		}

		currentPart.WriteByte(char)
	}

	// 處理最後一個部分
	part := strings.TrimSpace(currentPart.String())
	if part != "" {
		parts = append(parts, part)
	}

	return parts
}

// BuildWhereFromJSON 是一個便捷方法，直接返回 WhereBuilder
func BuildWhereFromJSON(jsonStr string) (*filters.WhereBuilder, error) {
	return JSONToWhereBuilder(jsonStr)
}

// convertWhereStringToJSONV2 是改進版的轉換函數
func convertWhereStringToJSONV2(whereStr string) (string, error) {
	// 移除外層大括號
	whereStr = strings.TrimPrefix(whereStr, "{")
	whereStr = strings.TrimSuffix(whereStr, "}")
	whereStr = strings.TrimSpace(whereStr)

	// 解析為 map
	result := parseWhereObject(whereStr)

	// 轉換為 JSON
	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("failed to convert to JSON: %w", err)
	}

	return string(jsonBytes), nil
}

// parseWhereObject 解析一個 where 對象
func parseWhereObject(str string) map[string]interface{} {
	result := make(map[string]interface{})

	// 解析各個字段
	tokens := tokenize(str)
	i := 0

	for i < len(tokens) {
		if i+1 < len(tokens) && tokens[i+1] == ":" {
			key := tokens[i]
			i += 2 // 跳過 key 和 :

			switch key {
			case "operator":
				if i < len(tokens) {
					result["operator"] = tokens[i]
					i++
				}
			case "path":
				if i < len(tokens) && tokens[i] == "[" {
					paths, consumed := parseArray(tokens[i:])
					result["path"] = paths
					i += consumed
				}
			case "valueText":
				if i < len(tokens) {
					if tokens[i] == "[" {
						values, consumed := parseArray(tokens[i:])
						result["valueText"] = values
						i += consumed
					} else if isQuotedString(tokens[i]) {
						result["valueText"] = []string{unquote(tokens[i])}
						i++
					}
				}
			case "valueNumber":
				if i < len(tokens) {
					if num, err := strconv.ParseFloat(tokens[i], 64); err == nil {
						result["valueNumber"] = num
						i++
					}
				}
			case "valueInt":
				if i < len(tokens) {
					if num, err := strconv.ParseInt(tokens[i], 10, 64); err == nil {
						result["valueInt"] = num
						i++
					}
				}
			case "valueBoolean":
				if i < len(tokens) {
					if tokens[i] == "true" || tokens[i] == "false" {
						result["valueBoolean"] = tokens[i] == "true"
						i++
					}
				}
			case "operands":
				if i < len(tokens) && tokens[i] == "[" {
					operands, consumed := parseOperandsArrayV2(tokens[i:])
					result["operands"] = operands
					i += consumed
				}
			default:
				i++
			}
		} else {
			i++
		}
	}

	return result
}

// tokenize 將字符串分解為標記
func tokenize(str string) []string {
	var tokens []string
	var current strings.Builder
	inQuotes := false
	escape := false

	for i := 0; i < len(str); i++ {
		char := str[i]

		if escape {
			current.WriteByte(char)
			escape = false
			continue
		}

		if char == '\\' {
			escape = true
			current.WriteByte(char)
			continue
		}

		if char == '"' {
			inQuotes = !inQuotes
			current.WriteByte(char)
			if !inQuotes && current.Len() > 0 {
				tokens = append(tokens, current.String())
				current.Reset()
			}
			continue
		}

		if inQuotes {
			current.WriteByte(char)
			continue
		}

		// 分隔符
		if char == ':' || char == '[' || char == ']' || char == '{' || char == '}' || char == ',' {
			if current.Len() > 0 {
				tokens = append(tokens, current.String())
				current.Reset()
			}
			tokens = append(tokens, string(char))
		} else if char == ' ' || char == '\t' || char == '\n' {
			if current.Len() > 0 {
				tokens = append(tokens, current.String())
				current.Reset()
			}
		} else {
			current.WriteByte(char)
		}
	}

	if current.Len() > 0 {
		tokens = append(tokens, current.String())
	}

	return tokens
}

// parseArray 解析數組
func parseArray(tokens []string) ([]string, int) {
	var result []string
	if len(tokens) == 0 || tokens[0] != "[" {
		return result, 0
	}

	i := 1
	for i < len(tokens) && tokens[i] != "]" {
		if tokens[i] == "," {
			i++
			continue
		}

		if isQuotedString(tokens[i]) {
			result = append(result, unquote(tokens[i]))
		} else {
			result = append(result, tokens[i])
		}
		i++
	}

	if i < len(tokens) && tokens[i] == "]" {
		i++
	}

	return result, i
}

// parseOperandsArrayV2 解析 operands 數組
func parseOperandsArrayV2(tokens []string) ([]map[string]interface{}, int) {
	var result []map[string]interface{}
	if len(tokens) == 0 || tokens[0] != "[" {
		return result, 0
	}

	i := 1
	for i < len(tokens) && tokens[i] != "]" {
		if tokens[i] == "," {
			i++
			continue
		}

		if tokens[i] == "{" {
			// 找到對應的 }
			start := i
			bracketCount := 1
			i++

			for i < len(tokens) && bracketCount > 0 {
				if tokens[i] == "{" {
					bracketCount++
				} else if tokens[i] == "}" {
					bracketCount--
				}
				i++
			}

			// 解析這個對象
			objTokens := tokens[start+1 : i-1]
			objStr := strings.Join(objTokens, " ")
			obj := parseWhereObject(objStr)
			if len(obj) > 0 {
				result = append(result, obj)
			}
		} else {
			i++
		}
	}

	if i < len(tokens) && tokens[i] == "]" {
		i++
	}

	return result, i
}

// isQuotedString 檢查是否是引號包裹的字符串
func isQuotedString(s string) bool {
	return strings.HasPrefix(s, `"`) && strings.HasSuffix(s, `"`)
}

// unquote 移除引號
func unquote(s string) string {
	if isQuotedString(s) {
		return s[1 : len(s)-1]
	}
	return s
}
