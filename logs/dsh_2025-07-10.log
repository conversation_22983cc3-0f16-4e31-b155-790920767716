2025-07-10T19:26:19.231+08:00 [INFO] [main.main] test_service_id_fix.go:20: === 測試 ServiceID 修復 ===
2025-07-10T19:26:19.231+08:00 [INFO] [main.testResourceRecordORM] test_service_id_fix.go:32: === 測試 ResourceRecord ORM 標籤 ===
2025-07-10T19:26:19.231+08:00 [INFO] [main.testResourceRecordORM] test_service_id_fix.go:59: ✅ ServiceID 已設置為默認值: dsh.svc
2025-07-10T19:26:19.232+08:00 [INFO] [main.testResourceRecordORM] test_service_id_fix.go:64: 測試數據結構: {
	"create_at": "2025-07-08T10:00:00Z",
	"file_storage_provider": "localFS",
	"id": "test_id_123",
	"plain_text_contents": null,
	"schema": "test_schema",
	"service_id": "dsh.svc",
	"table": "test_table",
	"update_at": "2025-07-08T10:00:00Z",
	"upload_files": [
		{
			"access_path": "/files/test_file.pdf",
			"access_url": "http://example.com/test_file.pdf",
			"content_type": "application/pdf",
			"file_name": "test_file.pdf",
			"size": 1024,
			"storage_bucket": "test_bucket"
		}
	],
	"upload_user_id": "test_user_456",
	"url_contents": null,
	"youtube_contents": null
}
2025-07-10T19:26:19.232+08:00 [INFO] [main.testResourceRecordORM] test_service_id_fix.go:68: 轉換為 Map 後的數據: map[create_at:2025-07-08T10:00:00Z file_storage_provider:localFS id:test_id_123 plain_text_contents:[] schema:test_schema service_id:dsh.svc table:test_table update_at:2025-07-08T10:00:00Z upload_files:[0x14000338f00] upload_user_id:test_user_456 url_contents:[] youtube_contents:[]]
2025-07-10T19:26:19.232+08:00 [INFO] [main.testResourceRecordORM] test_service_id_fix.go:72: ✅ service_id 字段存在於 Map 中: dsh.svc
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:79: === 測試 AMQP 消息處理邏輯 ===
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:113: 模擬 AMQP 消息: {"create_at":"2025-07-08T11:00:00Z","file_storage_provider":"localFS","id":"test_id_789","schema":"test_schema","service_id":"","table":"test_resources","update_at":"2025-07-08T11:00:00Z","upload_files":[{"access_path":"/files/test_document.pdf","access_url":"http://example.com/test_document.pdf","content_type":"application/pdf","file_name":"test_document.pdf","size":2048,"storage_bucket":"test_bucket"}],"upload_user_id":"test_user_789"}
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:124: 轉換後的 amsData.ServiceID: ''
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:129: ✅ ServiceID 已修復為: dsh.svc
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:134: 修復後的數據: {
	"create_at": "2025-07-08T11:00:00Z",
	"file_storage_provider": "localFS",
	"id": "test_id_789",
	"plain_text_contents": null,
	"schema": "test_schema",
	"service_id": "dsh.svc",
	"table": "test_resources",
	"update_at": "2025-07-08T11:00:00Z",
	"upload_files": [
		{
			"access_path": "/files/test_document.pdf",
			"access_url": "http://example.com/test_document.pdf",
			"content_type": "application/pdf",
			"file_name": "test_document.pdf",
			"size": 2048,
			"storage_bucket": "test_bucket"
		}
	],
	"upload_user_id": "test_user_789",
	"url_contents": null,
	"youtube_contents": null
}
2025-07-10T19:26:19.232+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:142: ✅ 字段 service_id 存在且有值: dsh.svc
2025-07-10T19:26:19.233+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:142: ✅ 字段 id 存在且有值: test_id_789
2025-07-10T19:26:19.233+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:142: ✅ 字段 upload_user_id 存在且有值: test_user_789
2025-07-10T19:26:19.233+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:142: ✅ 字段 create_at 存在且有值: 2025-07-08T11:00:00Z
2025-07-10T19:26:19.233+08:00 [INFO] [main.testAMQPMessageProcessing] test_service_id_fix.go:142: ✅ 字段 update_at 存在且有值: 2025-07-08T11:00:00Z
2025-07-10T19:26:19.233+08:00 [INFO] [main.main] test_service_id_fix.go:28: === 測試完成 ===
