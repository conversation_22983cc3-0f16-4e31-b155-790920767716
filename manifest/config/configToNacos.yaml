# https://goframe.org/docs/web/server-config-file-template
server:
  address:     ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

vectorEmbeddings:
  # 所採用的embedding 參數設定
  embedding:
    # 提供者： azure,google. google又分爲 studio,vertex
    provider: azure
    azure:
      resourceName:
      deploymentId:
      # 設定環境變量或者是key 推薦是環境變量名稱
      api_key:
    google:
      # provider 為google.studio 的時 projectId 不需要設置
      projectId:
      modelId:
      # provider 如果設定 google.studio 時候要設定studio api key
      # provider 如果設定 google.vertex 則採用設置環境變數到docker compose 的方式
      studioAPIKey:




  weaviate:
    host:  localhost:8080
    scheme: http
